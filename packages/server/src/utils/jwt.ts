import jwt, { SignOptions } from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

export interface JWTPayload {
  userId: string;
  email: string;
  username: string;
}

export function generateToken(payload: JWTPayload): string {
  const options: SignOptions = {
    expiresIn: JWT_EXPIRES_IN as string,
    issuer: 'nexus-design-tool',
    audience: 'nexus-users'
  };

  return jwt.sign(payload, JWT_SECRET, options);
}

export function verifyToken(token: string): JWTPayload {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'nexus-design-tool',
      audience: 'nexus-users'
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}

export function refreshToken(token: string): string {
  try {
    const decoded = verifyToken(token);
    // 生成新的token
    return generateToken({
      userId: decoded.userId,
      email: decoded.email,
      username: decoded.username
    });
  } catch (error) {
    throw new Error('Cannot refresh invalid token');
  }
}
