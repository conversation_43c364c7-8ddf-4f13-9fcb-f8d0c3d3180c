{"name": "@nexus/server", "version": "1.0.0", "description": "Nexus Design Tool - Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"Linting server code...\""}, "keywords": ["design-tool", "api", "koa", "typescript"], "author": "BingLiu", "license": "MIT", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/koa": "^2.15.0", "@types/koa__cors": "^5.0.0", "@types/koa__router": "^12.0.4", "@types/koa-bodyparser": "^4.3.12", "@types/koa-static": "^4.0.4", "@types/node": "^24.0.7", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@koa/cors": "^5.0.0", "@koa/router": "^13.1.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "koa": "^3.0.0", "koa-bodyparser": "^4.4.1", "koa-static": "^5.0.0", "mongoose": "^8.16.1", "socket.io": "^4.8.1", "uuid": "^11.1.0"}}