{"name": "@nexus/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@types/node": "^24.0.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "fabric": "^6.7.0", "konva": "^9.3.20", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-konva": "^19.0.5", "react-router-dom": "^7.6.3", "socket.io-client": "^4.8.1", "styled-components": "^6.1.19", "tailwindcss": "^3.4.17", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}