export const CANVAS_CONFIG = {
  // 默认尺寸
  DEFAULT_ELEMENT_SIZE: {
    width: 200,
    height: 200,
    radius: 50,
    strokeWidth: 2,
  },

  // 缩放限制
  ZOOM_LIMITS: {
    min: 0.1,
    max: 5,
    factor: 1.1,
  },

  // 默认颜色
  COLORS: {
    primary: '#3b82f6',
    stroke: '#2563eb',
    text: '#000000',
    placeholder: '#f0f0f0',
    placeholderStroke: '#ccc',
  },

  // 文本默认设置
  TEXT_DEFAULTS: {
    fontSize: 16,
    fontFamily: 'Arial',
    text: '文本',
    width: 100,
    height: 30,
  },

  // 画布设置
  CANVAS_SETTINGS: {
    leftPanelWidth: 288,
    rightPanelWidth: 288,
    toolbarHeight: 80,
  },

  // 触控板设置
  TOUCHPAD: {
    panTimeout: 200,
    scaleSensitivity: 0.01,
  },

  // 粘贴偏移
  PASTE_OFFSET: {
    x: 20,
    y: 20,
  },
} as const;
