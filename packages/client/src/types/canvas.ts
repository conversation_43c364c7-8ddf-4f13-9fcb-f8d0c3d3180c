export interface CanvasElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text' | 'image';
  x: number;
  y: number;
  width?: number;
  height?: number;
  radius?: number;
  points?: number[];
  text?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  visible?: boolean;
  locked?: boolean;
  rotation?: number;
  fontSize?: number;
  fontFamily?: string;
  src?: string; // 图片源
}

export interface CanvasProps {
  selectedTool: string;
  elements: CanvasElement[];
  selectedElements: string[];
  onElementSelect: (elementIds: string[]) => void;
  onElementAdd: (element: CanvasElement) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
  onToolChange: (tool: string) => void;
  scale: number;
  position: { x: number; y: number };
  onScaleChange: (scale: number) => void;
  onPositionChange: (position: { x: number; y: number }) => void;
}

export interface ContextMenuState {
  visible: boolean;
  x: number;
  y: number;
  target: 'canvas' | 'element';
  elementId?: string;
}

export type ToolType = 'select' | 'rectangle' | 'circle' | 'line' | 'text' | 'image';

export interface Point {
  x: number;
  y: number;
}

export interface SelectionRect {
  x: number;
  y: number;
  width: number;
  height: number;
}
