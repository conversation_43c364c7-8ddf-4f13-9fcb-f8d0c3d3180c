import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { CanvasElement } from '../types/canvas';
import { CANVAS_CONFIG } from '../config/canvas';

interface UseClipboardProps {
  elements: CanvasElement[];
  selectedElements: string[];
  onElementAdd: (element: CanvasElement) => void;
  onElementSelect: (elementIds: string[]) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
}

export const useClipboard = ({
  elements,
  selectedElements,
  onElementAdd,
  onElementSelect,
  onElementUpdate,
}: UseClipboardProps) => {
  const [clipboard, setClipboard] = useState<CanvasElement[]>([]);

  /**
   * 复制选中的元素
   */
  const handleCopy = useCallback(() => {
    const selectedElementsData = elements.filter(el => selectedElements.includes(el.id));
    setClipboard(selectedElementsData);
  }, [elements, selectedElements]);

  /**
   * 剪切选中的元素
   */
  const handleCut = useCallback(() => {
    const selectedElementsData = elements.filter(el => selectedElements.includes(el.id));
    setClipboard(selectedElementsData);
    
    // 删除原元素
    selectedElements.forEach(id => {
      const elementIndex = elements.findIndex(el => el.id === id);
      if (elementIndex !== -1) {
        elements.splice(elementIndex, 1);
      }
    });
    onElementSelect([]);
  }, [elements, selectedElements, onElementSelect]);

  /**
   * 粘贴剪贴板中的元素
   */
  const handlePaste = useCallback(() => {
    if (clipboard.length === 0) return;
    
    const newElements = clipboard.map(el => ({
      ...el,
      id: uuidv4(),
      x: el.x + CANVAS_CONFIG.PASTE_OFFSET.x,
      y: el.y + CANVAS_CONFIG.PASTE_OFFSET.y,
    }));
    
    newElements.forEach(el => onElementAdd(el));
    onElementSelect(newElements.map(el => el.id));
  }, [clipboard, onElementAdd, onElementSelect]);

  /**
   * 处理系统粘贴事件（图片）
   */
  const handleSystemPaste = useCallback(async (e: ClipboardEvent) => {
    e.preventDefault();
    
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      // 检查是否是图片
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          const reader = new FileReader();
          reader.onload = (event) => {
            const src = event.target?.result as string;
            if (src) {
              // 创建图片元素
              const imageElement: CanvasElement = {
                id: uuidv4(),
                type: 'image',
                x: 100, // 默认位置
                y: 100,
                width: CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.width,
                height: CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.height,
                src: src
              };
              
              onElementAdd(imageElement);
              onElementSelect([imageElement.id]);
            }
          };
          reader.readAsDataURL(file);
        }
        break;
      }
    }
  }, [onElementAdd, onElementSelect]);

  // 监听系统粘贴事件
  useEffect(() => {
    document.addEventListener('paste', handleSystemPaste);
    return () => document.removeEventListener('paste', handleSystemPaste);
  }, [handleSystemPaste]);

  return {
    clipboard,
    hasClipboard: clipboard.length > 0,
    handleCopy,
    handleCut,
    handlePaste,
  };
};
