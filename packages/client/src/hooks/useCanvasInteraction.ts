import { useState, useCallback, useRef, useEffect } from 'react';
import Kon<PERSON> from 'konva';
import { v4 as uuidv4 } from 'uuid';
import { CanvasElement, Point, SelectionRect, ToolType } from '../types/canvas';
import { CANVAS_CONFIG } from '../config/canvas';
import { getRelativePointerPosition, getScreenPointerPosition } from '../utils/coordinates';
import { getElementsInSelection } from '../utils/collision';

interface UseCanvasInteractionProps {
  selectedTool: ToolType;
  elements: CanvasElement[];
  selectedElements: string[];
  scale: number;
  position: Point;
  onElementAdd: (element: CanvasElement) => void;
  onElementSelect: (elementIds: string[]) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
  onToolChange: (tool: string) => void;
  onScaleChange: (scale: number) => void;
  onPositionChange: (position: Point) => void;
  onImageToolClick: (pos: Point) => void;
}

export const useCanvasInteraction = ({
  selectedTool,
  elements,
  selectedElements,
  scale,
  position,
  onElementAdd,
  onElementSelect,
  onElementUpdate,
  onToolChange,
  onScaleChange,
  onPositionChange,
  onImageToolClick,
}: UseCanvasInteractionProps) => {
  // 绘制状态
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawing, setCurrentDrawing] = useState<CanvasElement | null>(null);
  
  // 框选状态
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionRect, setSelectionRect] = useState<SelectionRect | null>(null);
  
  // 平移状态
  const [isPanning, setIsPanning] = useState(false);
  const [lastPointerPosition, setLastPointerPosition] = useState<Point | null>(null);
  const [isTouchpadPanning, setIsTouchpadPanning] = useState(false);
  const touchpadPanTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (touchpadPanTimeoutRef.current) {
        clearTimeout(touchpadPanTimeoutRef.current);
      }
    };
  }, []);

  /**
   * 开始绘制新元素
   */
  const handleStartDrawing = useCallback((pos: Point) => {
    if (selectedTool === 'select') return;

    const newElement: CanvasElement = {
      id: uuidv4(),
      type: selectedTool as any,
      x: pos.x,
      y: pos.y,
      fill: CANVAS_CONFIG.COLORS.primary,
      stroke: CANVAS_CONFIG.COLORS.stroke,
      strokeWidth: CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.strokeWidth,
      visible: true,
      locked: false,
    };

    switch (selectedTool) {
      case 'rectangle':
        newElement.width = 1;
        newElement.height = 1;
        break;
      case 'circle':
        newElement.radius = 1;
        break;
      case 'line':
        newElement.points = [0, 0, 0, 0];
        break;
      case 'text':
        newElement.text = CANVAS_CONFIG.TEXT_DEFAULTS.text;
        newElement.width = CANVAS_CONFIG.TEXT_DEFAULTS.width;
        newElement.height = CANVAS_CONFIG.TEXT_DEFAULTS.height;
        newElement.fontSize = CANVAS_CONFIG.TEXT_DEFAULTS.fontSize;
        newElement.fontFamily = CANVAS_CONFIG.TEXT_DEFAULTS.fontFamily;
        newElement.fill = CANVAS_CONFIG.COLORS.text;
        onElementAdd(newElement);
        return;
      case 'image':
        onImageToolClick(pos);
        return;
    }

    onElementAdd(newElement);
    setCurrentDrawing(newElement);
    setIsDrawing(true);
  }, [selectedTool, onElementAdd, onImageToolClick]);

  /**
   * 处理鼠标按下
   */
  const handleMouseDown = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    // 如果按住中键，开始平移
    if (e.evt.button === 1) {
      const screenPos = getScreenPointerPosition(stage);
      if (screenPos) {
        setIsPanning(true);
        setLastPointerPosition(screenPos);
      }
      return;
    }

    // 如果按下的是舞台背景
    if (e.target === stage) {
      if (selectedTool === 'select') {
        // 如果不是按住Ctrl/Cmd，清除当前选择
        if (!e.evt.ctrlKey && !e.evt.metaKey) {
          onElementSelect([]);
        }
        
        // 开始框选
        setIsSelecting(true);
        setSelectionRect({
          x: pos.x,
          y: pos.y,
          width: 0,
          height: 0,
        });
      } else {
        // 立即开始绘制新元素
        handleStartDrawing(pos);
      }
      return;
    }

    // 如果点击的是元素
    const clickedElement = e.target;
    const elementId = clickedElement.id();
    
    if (elementId && selectedTool === 'select') {
      if (e.evt.ctrlKey || e.evt.metaKey) {
        // Ctrl/Cmd + 点击：切换选择
        const newSelection = selectedElements.includes(elementId)
          ? selectedElements.filter(id => id !== elementId)
          : [...selectedElements, elementId];
        onElementSelect(newSelection);
      } else {
        // 普通点击：选择单个元素
        onElementSelect([elementId]);
      }
    }
  }, [selectedTool, selectedElements, onElementSelect, handleStartDrawing]);

  return {
    // 状态
    isDrawing,
    currentDrawing,
    isSelecting,
    selectionRect,
    isPanning,
    isTouchpadPanning,
    
    // 方法
    handleMouseDown,
    setIsDrawing,
    setCurrentDrawing,
    setIsSelecting,
    setSelectionRect,
    setIsPanning,
    setLastPointerPosition,
    setIsTouchpadPanning,
    touchpadPanTimeoutRef,
  };
};
