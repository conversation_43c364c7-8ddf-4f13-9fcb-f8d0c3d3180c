import { useState, useCallback } from 'react';

export const useImageManager = () => {
  const [imageCache, setImageCache] = useState<Map<string, HTMLImageElement>>(new Map());

  /**
   * 加载图片并缓存
   */
  const loadImage = useCallback((src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      // 检查缓存
      if (imageCache.has(src)) {
        resolve(imageCache.get(src)!);
        return;
      }

      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setImageCache(prev => new Map(prev.set(src, img)));
        resolve(img);
      };
      img.onerror = reject;
      img.src = src;
    });
  }, [imageCache]);

  /**
   * 获取缓存的图片
   */
  const getCachedImage = useCallback((src: string): HTMLImageElement | undefined => {
    return imageCache.get(src);
  }, [imageCache]);

  /**
   * 清理图片缓存
   */
  const clearImageCache = useCallback(() => {
    setImageCache(new Map());
  }, []);

  return {
    imageCache,
    loadImage,
    getCachedImage,
    clearImageCache,
  };
};
