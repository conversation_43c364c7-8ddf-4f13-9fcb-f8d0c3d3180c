import React, { useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  Sciss<PERSON>, 
  Clipboard, 
  Trash2, 
  <PERSON><PERSON>p, 
  MoveDown, 
  Eye, 
  EyeOff,
  Lock,
  Unlock,
  Group,
  Ungroup,
  RotateCw,
  FlipHorizontal,
  FlipVertical
} from 'lucide-react';

interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  shortcut?: string;
  disabled?: boolean;
  separator?: boolean;
  submenu?: ContextMenuItem[];
  onClick?: () => void;
}

interface ContextMenuProps {
  visible: boolean;
  x: number;
  y: number;
  items: ContextMenuItem[];
  onClose: () => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  visible,
  x,
  y,
  items,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  const handleItemClick = (item: ContextMenuItem) => {
    if (!item.disabled && item.onClick) {
      item.onClick();
      onClose();
    }
  };

  const renderMenuItem = (item: ContextMenuItem, index: number) => {
    if (item.separator) {
      return <div key={index} className="border-t border-gray-200 my-1" />;
    }

    return (
      <div
        key={item.id}
        className={`
          flex items-center justify-between px-3 py-2 text-sm cursor-pointer
          ${item.disabled 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
          }
        `}
        onClick={() => handleItemClick(item)}
      >
        <div className="flex items-center space-x-2">
          {item.icon && (
            <span className="w-4 h-4 flex-shrink-0">
              {item.icon}
            </span>
          )}
          <span>{item.label}</span>
        </div>
        {item.shortcut && (
          <span className="text-xs text-gray-400 ml-4">
            {item.shortcut}
          </span>
        )}
        {item.submenu && (
          <span className="text-gray-400 ml-2">▶</span>
        )}
      </div>
    );
  };

  // 计算菜单位置，确保不超出屏幕边界
  const getMenuPosition = () => {
    if (!menuRef.current) return { left: x, top: y };

    const menuRect = menuRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = x;
    let top = y;

    // 如果菜单会超出右边界，向左调整
    if (x + menuRect.width > viewportWidth) {
      left = x - menuRect.width;
    }

    // 如果菜单会超出下边界，向上调整
    if (y + menuRect.height > viewportHeight) {
      top = y - menuRect.height;
    }

    // 确保不会超出左边界和上边界
    left = Math.max(0, left);
    top = Math.max(0, top);

    return { left, top };
  };

  const position = getMenuPosition();

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-48"
      style={{
        left: position.left,
        top: position.top,
      }}
    >
      {items.map(renderMenuItem)}
    </div>
  );
};

// 预定义的菜单项工厂函数
export const createCanvasContextMenu = (
  hasSelection: boolean,
  hasClipboard: boolean,
  selectedElements: any[],
  onCopy: () => void,
  onCut: () => void,
  onPaste: () => void,
  onDelete: () => void,
  onBringToFront: () => void,
  onSendToBack: () => void,
  onGroup: () => void,
  onUngroup: () => void,
  onLock: () => void,
  onUnlock: () => void,
  onHide: () => void,
  onShow: () => void
): ContextMenuItem[] => {
  const items: ContextMenuItem[] = [];

  if (hasSelection) {
    // 编辑操作
    items.push(
      {
        id: 'copy',
        label: '复制',
        icon: <Copy className="w-4 h-4" />,
        shortcut: '⌘C',
        onClick: onCopy
      },
      {
        id: 'cut',
        label: '剪切',
        icon: <Scissors className="w-4 h-4" />,
        shortcut: '⌘X',
        onClick: onCut
      }
    );
  }

  // 粘贴选项
  items.push({
    id: 'paste',
    label: '粘贴',
    icon: <Clipboard className="w-4 h-4" />,
    shortcut: '⌘V',
    disabled: !hasClipboard,
    onClick: onPaste
  });

  // 如果没有选中元素，添加画布相关的菜单项
  if (!hasSelection) {
    items.push(
      { id: 'separator-canvas', separator: true },
      {
        id: 'select-all',
        label: '全选',
        shortcut: '⌘A',
        onClick: () => console.log('Select all')
      }
    );
  }

  if (hasSelection) {
    items.push(
      { id: 'separator1', separator: true },
      {
        id: 'delete',
        label: '删除',
        icon: <Trash2 className="w-4 h-4" />,
        shortcut: 'Delete',
        onClick: onDelete
      },
      { id: 'separator2', separator: true },
      // 图层操作
      {
        id: 'bring-to-front',
        label: '移到前面',
        icon: <MoveUp className="w-4 h-4" />,
        shortcut: '⌘]',
        onClick: onBringToFront
      },
      {
        id: 'send-to-back',
        label: '移到后面',
        icon: <MoveDown className="w-4 h-4" />,
        shortcut: '⌘[',
        onClick: onSendToBack
      },
      { id: 'separator3', separator: true }
    );

    // 组合操作
    if (selectedElements.length > 1) {
      items.push({
        id: 'group',
        label: '创建组合',
        icon: <Group className="w-4 h-4" />,
        shortcut: '⌘G',
        onClick: onGroup
      });
    }

    if (selectedElements.some(el => el.type === 'group')) {
      items.push({
        id: 'ungroup',
        label: '取消组合',
        icon: <Ungroup className="w-4 h-4" />,
        shortcut: '⌘⇧G',
        onClick: onUngroup
      });
    }

    items.push(
      { id: 'separator4', separator: true },
      // 显示/隐藏
      {
        id: 'hide',
        label: '隐藏',
        icon: <EyeOff className="w-4 h-4" />,
        shortcut: '⌘H',
        onClick: onHide
      },
      // 锁定/解锁
      {
        id: 'lock',
        label: '锁定',
        icon: <Lock className="w-4 h-4" />,
        shortcut: '⌘L',
        onClick: onLock
      }
    );
  }

  return items;
};
