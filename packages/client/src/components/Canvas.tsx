import React, { forwardRef, useRef, useCallback, useState, useEffect } from 'react';
import { Stage, Layer, Rect, Circle, Line, Text, Image, Transformer } from 'react-konva';
import Konva from 'konva';
import { v4 as uuidv4 } from 'uuid';
import { ContextMenu, createCanvasContextMenu } from './ContextMenu';

interface CanvasElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text' | 'image';
  x: number;
  y: number;
  width?: number;
  height?: number;
  radius?: number;
  points?: number[];
  text?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  visible?: boolean;
  locked?: boolean;
  rotation?: number;
  fontSize?: number;
  fontFamily?: string;
  src?: string; // 图片源
}

interface CanvasProps {
  selectedTool: string;
  elements: CanvasElement[];
  selectedElements: string[];
  onElementSelect: (elementIds: string[]) => void;
  onElementAdd: (element: CanvasElement) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
  onToolChange: (tool: string) => void;
  scale: number;
  position: { x: number; y: number };
  onScaleChange: (scale: number) => void;
  onPositionChange: (position: { x: number; y: number }) => void;
}

export const Canvas = forwardRef<Konva.Stage, CanvasProps>(({
  selectedTool,
  elements,
  selectedElements,
  onElementSelect,
  onElementAdd,
  onElementUpdate,
  onToolChange,
  scale,
  position,
  onScaleChange,
  onPositionChange,
}, ref) => {
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawing, setCurrentDrawing] = useState<CanvasElement | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionRect, setSelectionRect] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastPointerPosition, setLastPointerPosition] = useState<{ x: number; y: number } | null>(null);
  const [isTouchpadPanning, setIsTouchpadPanning] = useState(false);
  const touchpadPanTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    target: 'canvas' | 'element';
    elementId?: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    target: 'canvas'
  });
  const [clipboard, setClipboard] = useState<CanvasElement[]>([]);
  const [imageCache, setImageCache] = useState<Map<string, HTMLImageElement>>(new Map());
  const [stageSize, setStageSize] = useState({
    width: window.innerWidth - 576, // 减去左右面板宽度 (288 * 2)
    height: window.innerHeight - 80  // 减去工具栏高度
  });

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setStageSize({
        width: window.innerWidth - 576, // 减去左右面板宽度
        height: window.innerHeight - 80  // 减去工具栏高度
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (touchpadPanTimeoutRef.current) {
        clearTimeout(touchpadPanTimeoutRef.current);
      }
    };
  }, []);

  // 图片加载函数
  const loadImage = useCallback((src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      // 检查缓存
      if (imageCache.has(src)) {
        resolve(imageCache.get(src)!);
        return;
      }

      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setImageCache(prev => new Map(prev.set(src, img)));
        resolve(img);
      };
      img.onerror = reject;
      img.src = src;
    });
  }, [imageCache]);

  // 处理粘贴事件
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      e.preventDefault();

      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        // 检查是否是图片
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
              const src = event.target?.result as string;
              if (src) {
                // 创建图片元素
                const imageElement: CanvasElement = {
                  id: uuidv4(),
                  type: 'image',
                  x: 100, // 默认位置
                  y: 100,
                  width: 200, // 默认尺寸，后续会根据实际图片调整
                  height: 200,
                  src: src
                };

                onElementAdd(imageElement);
                onElementSelect([imageElement.id]);
              }
            };
            reader.readAsDataURL(file);
          }
          break;
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [onElementAdd, onElementSelect]);

  // 处理图片工具点击
  const handleImageToolClick = useCallback((pos: { x: number; y: number }) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const src = event.target?.result as string;
          if (src) {
            // 创建图片元素
            const imageElement: CanvasElement = {
              id: uuidv4(),
              type: 'image',
              x: pos.x,
              y: pos.y,
              width: 200, // 默认尺寸
              height: 200,
              src: src
            };

            onElementAdd(imageElement);
            onElementSelect([imageElement.id]);
            onToolChange('select'); // 自动切换到选择工具
          }
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  }, [onElementAdd, onElementSelect, onToolChange]);

  // 处理滚轮和触控板手势
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();

    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const { deltaX, deltaY, ctrlKey } = e.evt;

    // 检测是否是触控板的捏合手势（缩放）
    // ctrlKey 在 Mac 上表示触控板的捏合手势
    if (ctrlKey) {
      // 如果正在绘制、框选或触控板平移，禁止缩放
      if (isDrawing || isSelecting || isTouchpadPanning) {
        return;
      }

      // 触控板捏合缩放
      const oldScale = stage.scaleX();
      const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
      };

      // 触控板的 deltaY 值通常比较小，需要调整敏感度
      const scaleFactor = 1 + Math.abs(deltaY) * 0.01;
      const direction = deltaY > 0 ? -1 : 1;
      const newScale = direction > 0 ? oldScale * scaleFactor : oldScale / scaleFactor;

      // 限制缩放范围
      const clampedScale = Math.max(0.1, Math.min(5, newScale));

      const newPos = {
        x: pointer.x - mousePointTo.x * clampedScale,
        y: pointer.y - mousePointTo.y * clampedScale,
      };

      onScaleChange(clampedScale);
      onPositionChange(newPos);
      return;
    }

    // 更精确的触控板检测
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // 如果有水平移动，优先认为是触控板平移
    if (absDeltaX > 0) {
      // 设置触控板平移状态
      setIsTouchpadPanning(true);

      // 清除之前的定时器
      if (touchpadPanTimeoutRef.current) {
        clearTimeout(touchpadPanTimeoutRef.current);
      }

      // 设置新的定时器来重置状态
      touchpadPanTimeoutRef.current = setTimeout(() => {
        setIsTouchpadPanning(false);
      }, 200);

      // 触控板两指拖拽平移 - 总是允许
      const newPos = {
        x: position.x - deltaX,
        y: position.y - deltaY,
      };
      onPositionChange(newPos);
      return; // 重要：直接返回，不执行后续的缩放逻辑
    }

    // 只有在纯垂直滚动且没有水平移动时才进行缩放
    if (absDeltaY > 0 && absDeltaX === 0) {
      // 鼠标滚轮缩放（纯垂直滚动）
      // 如果正在绘制、框选或触控板平移，禁止缩放
      if (isDrawing || isSelecting || isTouchpadPanning) {
        return;
      }

      const oldScale = stage.scaleX();
      const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
      };

      const direction = deltaY > 0 ? -1 : 1;
      const factor = 1.1;
      const newScale = direction > 0 ? oldScale * factor : oldScale / factor;

      // 限制缩放范围
      const clampedScale = Math.max(0.1, Math.min(5, newScale));

      const newPos = {
        x: pointer.x - mousePointTo.x * clampedScale,
        y: pointer.y - mousePointTo.y * clampedScale,
      };

      onScaleChange(clampedScale);
      onPositionChange(newPos);
    }
  }, [isDrawing, isSelecting, isTouchpadPanning, position, onScaleChange, onPositionChange]);

  // 获取相对于画布的鼠标位置（考虑缩放和平移）
  const getRelativePointerPosition = useCallback((stage: Konva.Stage) => {
    const pointer = stage.getPointerPosition();
    if (!pointer) return null;

    // 转换为画布坐标系
    const transform = stage.getAbsoluteTransform().copy();
    transform.invert();
    return transform.point(pointer);
  }, []);

  // 处理右键菜单
  const handleContextMenu = useCallback((e: Konva.KonvaEventObject<PointerEvent>) => {
    e.evt.preventDefault();

    const stage = e.target.getStage();
    if (!stage) return;

    // 获取鼠标在页面中的位置（屏幕坐标）
    const clientX = e.evt.clientX;
    const clientY = e.evt.clientY;

    // 检查是否点击在元素上
    const clickedElement = e.target;
    const elementId = clickedElement.id();

    if (elementId && e.target !== stage) {
      // 右键点击元素
      if (!selectedElements.includes(elementId)) {
        // 如果点击的元素未被选中，则选中它
        onElementSelect([elementId]);
      }

      setContextMenu({
        visible: true,
        x: clientX,
        y: clientY,
        target: 'element',
        elementId
      });
    } else {
      // 右键点击画布
      setContextMenu({
        visible: true,
        x: clientX,
        y: clientY,
        target: 'canvas'
      });
    }
  }, [selectedElements, onElementSelect]);

  // 处理鼠标按下
  const handleMouseDown = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    // 隐藏右键菜单
    setContextMenu(prev => ({ ...prev, visible: false }));

    const stage = e.target.getStage();
    if (!stage) return;

    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    // 如果按住中键，开始平移（使用屏幕坐标）
    if (e.evt.button === 1) {
      const screenPos = stage.getPointerPosition();
      if (screenPos) {
        setIsPanning(true);
        setLastPointerPosition(screenPos);
      }
      return;
    }

    // 如果按下的是舞台背景
    if (e.target === stage) {
      if (selectedTool === 'select') {
        // 如果不是按住Ctrl/Cmd，清除当前选择
        if (!e.evt.ctrlKey && !e.evt.metaKey) {
          onElementSelect([]);
        }

        // 开始框选
        setIsSelecting(true);
        setSelectionRect({
          x: pos.x,
          y: pos.y,
          width: 0,
          height: 0,
        });
      } else {
        // 立即开始绘制新元素
        handleStartDrawing(pos);
      }
      return;
    }

    // 如果点击的是元素
    const clickedElement = e.target;
    const elementId = clickedElement.id();
    
    if (selectedTool === 'select' && elementId) {
      // 处理元素选择
      if (e.evt.ctrlKey || e.evt.metaKey) {
        // 多选
        const newSelection = selectedElements.includes(elementId)
          ? selectedElements.filter(id => id !== elementId)
          : [...selectedElements, elementId];
        onElementSelect(newSelection);
      } else {
        // 单选
        onElementSelect([elementId]);
      }
    }
  }, [selectedTool, selectedElements, onElementSelect, getRelativePointerPosition]);

  // 开始绘制
  const handleStartDrawing = useCallback((pos: { x: number; y: number }) => {
    if (selectedTool === 'select') return;

    const newElement: CanvasElement = {
      id: uuidv4(),
      type: selectedTool as any,
      x: pos.x,
      y: pos.y,
      fill: '#3b82f6',
      stroke: '#2563eb',
      strokeWidth: 2,
      visible: true,
      locked: false,
    };

    switch (selectedTool) {
      case 'rectangle':
        newElement.width = 1; // 初始最小尺寸
        newElement.height = 1;
        break;
      case 'circle':
        newElement.radius = 1; // 初始最小半径
        break;
      case 'line':
        newElement.points = [0, 0, 0, 0];
        break;
      case 'text':
        newElement.text = '文本';
        newElement.width = 100;
        newElement.height = 30;
        newElement.fontSize = 16;
        newElement.fontFamily = 'Arial';
        newElement.fill = '#000000';
        onElementAdd(newElement);
        return;
      case 'image':
        handleImageToolClick(pos);
        return;
    }

    // 立即添加元素到画布，这样就能在拖拽时看到实时效果
    onElementAdd(newElement);
    setCurrentDrawing(newElement);
    setIsDrawing(true);
  }, [selectedTool, onElementAdd]);

  // 绘制过程中
  const handleMouseMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const screenPos = stage.getPointerPosition();
    if (!screenPos) return;

    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    // 处理平移（使用屏幕坐标）
    if (isPanning && lastPointerPosition) {
      const dx = screenPos.x - lastPointerPosition.x;
      const dy = screenPos.y - lastPointerPosition.y;

      const newPos = {
        x: position.x + dx,
        y: position.y + dy,
      };

      onPositionChange(newPos);
      setLastPointerPosition(screenPos);
      return;
    }

    // 处理框选（使用画布坐标）
    if (isSelecting && selectionRect) {
      const newRect = {
        x: Math.min(pos.x, selectionRect.x),
        y: Math.min(pos.y, selectionRect.y),
        width: Math.abs(pos.x - selectionRect.x),
        height: Math.abs(pos.y - selectionRect.y),
      };
      setSelectionRect(newRect);
      return;
    }

    // 处理绘制
    if (!isDrawing || !currentDrawing) return;

    const updatedElement = { ...currentDrawing };
    const deltaX = pos.x - currentDrawing.x;
    const deltaY = pos.y - currentDrawing.y;

    switch (currentDrawing.type) {
      case 'rectangle':
        updatedElement.width = Math.abs(deltaX);
        updatedElement.height = Math.abs(deltaY);
        if (deltaX < 0) updatedElement.x = pos.x;
        if (deltaY < 0) updatedElement.y = pos.y;
        break;
      case 'circle':
        updatedElement.radius = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        break;
      case 'line':
        updatedElement.points = [0, 0, deltaX, deltaY];
        break;
    }

    // 实时更新画布上的元素
    setCurrentDrawing(updatedElement);
    onElementUpdate(updatedElement.id, updatedElement);
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, lastPointerPosition, position, onElementUpdate, onPositionChange, getRelativePointerPosition]);

  // 结束绘制或框选
  const handleMouseUp = useCallback(() => {
    // 处理平移结束
    if (isPanning) {
      setIsPanning(false);
      setLastPointerPosition(null);
      return;
    }

    // 处理框选完成
    if (isSelecting && selectionRect) {
      // 找到框选区域内的所有元素
      const selectedIds: string[] = [];

      elements.forEach(element => {
        // 检查元素是否在框选区域内
        const elementBounds = {
          x: element.x,
          y: element.y,
          width: element.width || (element.radius ? element.radius * 2 : 0),
          height: element.height || (element.radius ? element.radius * 2 : 0),
        };

        // 简单的矩形碰撞检测
        if (
          elementBounds.x < selectionRect.x + selectionRect.width &&
          elementBounds.x + elementBounds.width > selectionRect.x &&
          elementBounds.y < selectionRect.y + selectionRect.height &&
          elementBounds.y + elementBounds.height > selectionRect.y
        ) {
          selectedIds.push(element.id);
        }
      });

      // 更新选择
      onElementSelect(selectedIds);

      // 清理框选状态
      setIsSelecting(false);
      setSelectionRect(null);
      return;
    }

    // 处理绘制完成
    if (isDrawing && currentDrawing) {
      // 绘制完成，清理状态
      setIsDrawing(false);
      setCurrentDrawing(null);

      // 选择刚创建的元素
      onElementSelect([currentDrawing.id]);

      // 自动切换到选择工具
      onToolChange('select');
    }
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, elements, onElementSelect, onToolChange]);

  // 右键菜单操作函数
  const handleCopy = useCallback(() => {
    const selectedElementsData = elements.filter(el => selectedElements.includes(el.id));
    setClipboard(selectedElementsData);
  }, [elements, selectedElements]);

  const handleCut = useCallback(() => {
    const selectedElementsData = elements.filter(el => selectedElements.includes(el.id));
    setClipboard(selectedElementsData);
    selectedElements.forEach(id => {
      const elementIndex = elements.findIndex(el => el.id === id);
      if (elementIndex !== -1) {
        elements.splice(elementIndex, 1);
      }
    });
    onElementSelect([]);
  }, [elements, selectedElements, onElementSelect]);

  const handlePaste = useCallback(() => {
    if (clipboard.length === 0) return;

    const newElements = clipboard.map(el => ({
      ...el,
      id: uuidv4(),
      x: el.x + 20, // 偏移一点位置
      y: el.y + 20
    }));

    newElements.forEach(el => onElementAdd(el));
    onElementSelect(newElements.map(el => el.id));
  }, [clipboard, onElementAdd, onElementSelect]);

  const handleDelete = useCallback(() => {
    selectedElements.forEach(id => {
      const elementIndex = elements.findIndex(el => el.id === id);
      if (elementIndex !== -1) {
        elements.splice(elementIndex, 1);
      }
    });
    onElementSelect([]);
  }, [elements, selectedElements, onElementSelect]);

  const handleBringToFront = useCallback(() => {
    // TODO: 实现图层前移
    console.log('Bring to front');
  }, []);

  const handleSendToBack = useCallback(() => {
    // TODO: 实现图层后移
    console.log('Send to back');
  }, []);

  const handleGroup = useCallback(() => {
    // TODO: 实现组合
    console.log('Group elements');
  }, []);

  const handleUngroup = useCallback(() => {
    // TODO: 实现取消组合
    console.log('Ungroup elements');
  }, []);

  const handleLock = useCallback(() => {
    // TODO: 实现锁定
    console.log('Lock elements');
  }, []);

  const handleUnlock = useCallback(() => {
    // TODO: 实现解锁
    console.log('Unlock elements');
  }, []);

  const handleHide = useCallback(() => {
    // TODO: 实现隐藏
    console.log('Hide elements');
  }, []);

  const handleShow = useCallback(() => {
    // TODO: 实现显示
    console.log('Show elements');
  }, []);

  // 处理元素变换
  const handleTransformEnd = useCallback((e: Konva.KonvaEventObject<Event>) => {
    const node = e.target;
    const elementId = node.id();
    
    if (elementId) {
      const updates: Partial<CanvasElement> = {
        x: node.x(),
        y: node.y(),
        rotation: node.rotation(),
      };

      if (node.width) updates.width = node.width() * node.scaleX();
      if (node.height) updates.height = node.height() * node.scaleY();
      if ('radius' in node && typeof (node as any).radius === 'function') {
        updates.radius = (node as any).radius() * node.scaleX();
      }

      // 重置缩放
      node.scaleX(1);
      node.scaleY(1);

      onElementUpdate(elementId, updates);
    }
  }, [onElementUpdate]);

  // 渲染元素
  const renderElement = useCallback((element: CanvasElement) => {
    const commonProps = {
      key: element.id,
      id: element.id,
      x: element.x,
      y: element.y,
      fill: element.fill,
      stroke: element.stroke,
      strokeWidth: element.strokeWidth,
      draggable: selectedTool === 'select',
      onTransformEnd: handleTransformEnd,
    };

    switch (element.type) {
      case 'rectangle':
        return (
          <Rect
            {...commonProps}
            width={element.width || 0}
            height={element.height || 0}
          />
        );
      case 'circle':
        return (
          <Circle
            {...commonProps}
            radius={element.radius || 0}
          />
        );
      case 'line':
        return (
          <Line
            {...commonProps}
            points={element.points || [0, 0, 0, 0]}
          />
        );
      case 'text':
        return (
          <Text
            {...commonProps}
            text={element.text || ''}
            fontSize={16}
            width={element.width}
            height={element.height}
          />
        );
      case 'image':
        const cachedImage = imageCache.get(element.src || '');
        if (cachedImage) {
          return (
            <Image
              {...commonProps}
              image={cachedImage}
              width={element.width || cachedImage.width}
              height={element.height || cachedImage.height}
            />
          );
        } else if (element.src) {
          // 异步加载图片
          loadImage(element.src).then(() => {
            // 触发重新渲染
            onElementUpdate(element.id, {});
          });
          // 显示占位符
          return (
            <Rect
              {...commonProps}
              width={element.width || 200}
              height={element.height || 200}
              fill="#f0f0f0"
              stroke="#ccc"
              strokeWidth={1}
            />
          );
        }
        return null;
      default:
        return null;
    }
  }, [selectedTool, handleTransformEnd, imageCache, loadImage, onElementUpdate]);

  // 更新变换器
  React.useEffect(() => {
    if (transformerRef.current && stageRef.current) {
      const transformer = transformerRef.current;
      const stage = stageRef.current;
      
      if (selectedElements.length > 0) {
        const nodes = selectedElements.map(id => stage.findOne(`#${id}`)).filter(Boolean) as Konva.Node[];
        transformer.nodes(nodes);
      } else {
        transformer.nodes([]);
      }
      
      transformer.getLayer()?.batchDraw();
    }
  }, [selectedElements]);

  return (
    <div className="w-full h-full bg-gray-100 relative overflow-hidden">
      {/* 画布背景网格 */}
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />

      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={scale}
        scaleY={scale}
        x={position.x}
        y={position.y}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        onContextMenu={handleContextMenu}
      >
        <Layer>
          {/* 渲染所有元素 */}
          {elements.map(renderElement)}

          {/* 框选矩形 */}
          {selectionRect && (
            <Rect
              x={selectionRect.x}
              y={selectionRect.y}
              width={selectionRect.width}
              height={selectionRect.height}
              fill="rgba(59, 130, 246, 0.1)"
              stroke="#3b82f6"
              strokeWidth={1}
              dash={[5, 5]}
            />
          )}

          {/* 变换器 */}
          <Transformer ref={transformerRef} />
        </Layer>
      </Stage>

      {/* 右键菜单 */}
      <ContextMenu
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        items={createCanvasContextMenu(
          selectedElements.length > 0,
          clipboard.length > 0,
          elements.filter(el => selectedElements.includes(el.id)),
          handleCopy,
          handleCut,
          handlePaste,
          handleDelete,
          handleBringToFront,
          handleSendToBack,
          handleGroup,
          handleUngroup,
          handleLock,
          handleUnlock,
          handleHide,
          handleShow
        )}
        onClose={() => setContextMenu(prev => ({ ...prev, visible: false }))}
      />
    </div>
  );
});
