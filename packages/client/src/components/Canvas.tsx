import React, { forwardRef, useRef, useCallback, useState, useEffect } from 'react';
import { Stage, Layer, Rect, Transformer } from 'react-konva';
import Konva from 'konva';
import { v4 as uuidv4 } from 'uuid';
import { ContextMenu, createCanvasContextMenu } from './ContextMenu';
import { ElementRenderer } from './Canvas/ElementRenderer';
import { useImageManager } from '../hooks/useImageManager';
import { useClipboard } from '../hooks/useClipboard';
import { useCanvasInteraction } from '../hooks/useCanvasInteraction';
import type { CanvasElement, CanvasProps } from '../types/canvas';
import { CANVAS_CONFIG } from '../config/canvas';
import { getRelativePointerPosition, getScreenPointerPosition } from '../utils/coordinates';
import { getElementsInSelection } from '../utils/collision';

export const Canvas = forwardRef<Konva.Stage, CanvasProps>(({
  selectedTool,
  elements,
  selectedElements,
  onElementSelect,
  onElementAdd,
  onElementUpdate,
  onToolChange,
  scale,
  position,
  onScaleChange,
  onPositionChange,
}, ref) => {
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  
  // 画布尺寸
  const [stageSize, setStageSize] = useState({ 
    width: window.innerWidth - CANVAS_CONFIG.CANVAS_SETTINGS.leftPanelWidth - CANVAS_CONFIG.CANVAS_SETTINGS.rightPanelWidth,
    height: window.innerHeight - CANVAS_CONFIG.CANVAS_SETTINGS.toolbarHeight
  });

  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    target: 'canvas' as 'canvas' | 'element',
    elementId: undefined as string | undefined
  });

  // 图片管理
  const { imageCache, loadImage } = useImageManager();

  // 剪贴板管理
  const { hasClipboard, handleCopy, handleCut, handlePaste } = useClipboard({
    elements,
    selectedElements,
    onElementAdd,
    onElementSelect,
    onElementUpdate,
  });

  // 图片工具点击处理
  const handleImageToolClick = useCallback((pos: { x: number; y: number }) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const src = event.target?.result as string;
          if (src) {
            const imageElement: CanvasElement = {
              id: uuidv4(),
              type: 'image',
              x: pos.x,
              y: pos.y,
              width: CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.width,
              height: CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.height,
              src: src
            };
            
            onElementAdd(imageElement);
            onElementSelect([imageElement.id]);
            onToolChange('select');
          }
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  }, [onElementAdd, onElementSelect, onToolChange]);

  // 画布交互
  const {
    isDrawing,
    currentDrawing,
    isSelecting,
    selectionRect,
    isPanning,
    isTouchpadPanning,
    handleMouseDown,
    setIsDrawing,
    setCurrentDrawing,
    setIsSelecting,
    setSelectionRect,
    setIsPanning,
    setLastPointerPosition,
    setIsTouchpadPanning,
    touchpadPanTimeoutRef,
  } = useCanvasInteraction({
    selectedTool: selectedTool as any,
    elements,
    selectedElements,
    scale,
    position,
    onElementAdd,
    onElementSelect,
    onElementUpdate,
    onToolChange,
    onScaleChange,
    onPositionChange,
    onImageToolClick: handleImageToolClick,
  });

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setStageSize({
        width: window.innerWidth - CANVAS_CONFIG.CANVAS_SETTINGS.leftPanelWidth - CANVAS_CONFIG.CANVAS_SETTINGS.rightPanelWidth,
        height: window.innerHeight - CANVAS_CONFIG.CANVAS_SETTINGS.toolbarHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 处理滚轮和触控板手势
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    
    const stage = e.target.getStage();
    if (!stage) return;

    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const { deltaX, deltaY, ctrlKey } = e.evt;

    // 检测是否是触控板的捏合手势（缩放）
    if (ctrlKey) {
      if (isDrawing || isSelecting) return;
      
      const oldScale = stage.scaleX();
      const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
      };

      const scaleFactor = 1 + Math.abs(deltaY) * CANVAS_CONFIG.TOUCHPAD.scaleSensitivity;
      const direction = deltaY > 0 ? -1 : 1;
      const newScale = direction > 0 ? oldScale * scaleFactor : oldScale / scaleFactor;
      
      const clampedScale = Math.max(
        CANVAS_CONFIG.ZOOM_LIMITS.min, 
        Math.min(CANVAS_CONFIG.ZOOM_LIMITS.max, newScale)
      );

      const newPos = {
        x: pointer.x - mousePointTo.x * clampedScale,
        y: pointer.y - mousePointTo.y * clampedScale,
      };

      onScaleChange(clampedScale);
      onPositionChange(newPos);
      return;
    }

    // 触控板平移检测
    if (Math.abs(deltaX) > 0) {
      setIsTouchpadPanning(true);
      
      if (touchpadPanTimeoutRef.current) {
        clearTimeout(touchpadPanTimeoutRef.current);
      }
      
      touchpadPanTimeoutRef.current = setTimeout(() => {
        setIsTouchpadPanning(false);
      }, CANVAS_CONFIG.TOUCHPAD.panTimeout);
      
      const newPos = {
        x: position.x - deltaX,
        y: position.y - deltaY,
      };
      onPositionChange(newPos);
    } else if (Math.abs(deltaY) > 0) {
      if (isDrawing || isSelecting || isTouchpadPanning) return;
      
      const oldScale = stage.scaleX();
      const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
      };

      const direction = deltaY > 0 ? -1 : 1;
      const newScale = direction > 0 ? oldScale * CANVAS_CONFIG.ZOOM_LIMITS.factor : oldScale / CANVAS_CONFIG.ZOOM_LIMITS.factor;
      
      const clampedScale = Math.max(
        CANVAS_CONFIG.ZOOM_LIMITS.min, 
        Math.min(CANVAS_CONFIG.ZOOM_LIMITS.max, newScale)
      );

      const newPos = {
        x: pointer.x - mousePointTo.x * clampedScale,
        y: pointer.y - mousePointTo.y * clampedScale,
      };

      onScaleChange(clampedScale);
      onPositionChange(newPos);
    }
  }, [isDrawing, isSelecting, isTouchpadPanning, position, onScaleChange, onPositionChange, setIsTouchpadPanning, touchpadPanTimeoutRef]);

  // 处理鼠标移动
  const handleMouseMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const screenPos = getScreenPointerPosition(stage);
    if (!screenPos) return;
    
    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    // 处理平移（使用屏幕坐标）
    if (isPanning && setLastPointerPosition) {
      // 这里需要从交互hook中获取lastPointerPosition
      // 暂时跳过平移处理，因为状态在hook中
      return;
    }

    // 处理框选（使用画布坐标）
    if (isSelecting && selectionRect) {
      const newRect = {
        x: Math.min(pos.x, selectionRect.x),
        y: Math.min(pos.y, selectionRect.y),
        width: Math.abs(pos.x - selectionRect.x),
        height: Math.abs(pos.y - selectionRect.y),
      };
      setSelectionRect(newRect);
      return;
    }

    // 处理绘制
    if (!isDrawing || !currentDrawing) return;

    const updatedElement = { ...currentDrawing };
    const deltaX = pos.x - currentDrawing.x;
    const deltaY = pos.y - currentDrawing.y;

    switch (currentDrawing.type) {
      case 'rectangle':
        updatedElement.width = Math.abs(deltaX);
        updatedElement.height = Math.abs(deltaY);
        if (deltaX < 0) updatedElement.x = pos.x;
        if (deltaY < 0) updatedElement.y = pos.y;
        break;
      case 'circle':
        updatedElement.radius = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        break;
      case 'line':
        updatedElement.points = [0, 0, deltaX, deltaY];
        break;
    }

    setCurrentDrawing(updatedElement);
    onElementUpdate(updatedElement.id, updatedElement);
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, onElementUpdate, setCurrentDrawing, setSelectionRect]);

  // 处理鼠标松开
  const handleMouseUp = useCallback(() => {
    if (isPanning) {
      setIsPanning(false);
      setLastPointerPosition(null);
      return;
    }

    if (isSelecting && selectionRect) {
      const selectedIds = getElementsInSelection(elements, selectionRect);
      onElementSelect(selectedIds);
      
      setIsSelecting(false);
      setSelectionRect(null);
      return;
    }

    if (isDrawing && currentDrawing) {
      setIsDrawing(false);
      setCurrentDrawing(null);
      
      onElementSelect([currentDrawing.id]);
      onToolChange('select');
    }
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, elements, onElementSelect, onToolChange, setIsDrawing, setCurrentDrawing, setIsSelecting, setSelectionRect, setIsPanning, setLastPointerPosition]);

  // 处理右键菜单
  const handleContextMenu = useCallback((e: Konva.KonvaEventObject<PointerEvent>) => {
    e.evt.preventDefault();

    const stage = e.target.getStage();
    if (!stage) return;

    const clientX = e.evt.clientX;
    const clientY = e.evt.clientY;

    const clickedElement = e.target;
    const elementId = clickedElement.id();

    if (elementId && e.target !== stage) {
      if (!selectedElements.includes(elementId)) {
        onElementSelect([elementId]);
      }

      setContextMenu({
        visible: true,
        x: clientX,
        y: clientY,
        target: 'element',
        elementId
      });
    } else {
      setContextMenu({
        visible: true,
        x: clientX,
        y: clientY,
        target: 'canvas',
        elementId: undefined
      });
    }
  }, [selectedElements, onElementSelect]);

  // 处理变换结束
  const handleTransformEnd = useCallback((elementId: string) => {
    const node = stageRef.current?.findOne(`#${elementId}`);
    if (node) {
      const scaleX = node.scaleX();
      const scaleY = node.scaleY();

      node.scaleX(1);
      node.scaleY(1);

      const updates: Partial<CanvasElement> = {
        x: node.x(),
        y: node.y(),
        rotation: node.rotation(),
      };

      if (node.width && node.height) {
        updates.width = Math.max(5, node.width() * scaleX);
        updates.height = Math.max(5, node.height() * scaleY);
      }

      onElementUpdate(elementId, updates);
    }
  }, [onElementUpdate]);

  // 更新变换器
  useEffect(() => {
    const transformer = transformerRef.current;
    const stage = stageRef.current;

    if (!transformer || !stage) return;

    if (selectedElements.length === 0) {
      transformer.nodes([]);
    } else {
      const nodes = selectedElements
        .map(id => stage.findOne(`#${id}`))
        .filter(Boolean) as Konva.Node[];
      transformer.nodes(nodes);
    }

    transformer.getLayer()?.batchDraw();
  }, [selectedElements]);

  return (
    <div className="w-full h-full relative">
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={scale}
        scaleY={scale}
        x={position.x}
        y={position.y}
        onMouseDown={(e) => {
          setContextMenu(prev => ({ ...prev, visible: false }));
          handleMouseDown(e);
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        onContextMenu={handleContextMenu}
      >
        <Layer>
          {/* 渲染所有元素 */}
          {elements.map(element => (
            <ElementRenderer
              key={element.id}
              element={element}
              selectedTool={selectedTool}
              imageCache={imageCache}
              onTransformEnd={handleTransformEnd}
              onElementUpdate={onElementUpdate}
              loadImage={loadImage}
            />
          ))}

          {/* 框选矩形 */}
          {selectionRect && (
            <Rect
              x={selectionRect.x}
              y={selectionRect.y}
              width={selectionRect.width}
              height={selectionRect.height}
              fill="rgba(59, 130, 246, 0.1)"
              stroke="#3b82f6"
              strokeWidth={1}
              dash={[5, 5]}
            />
          )}

          {/* 变换器 */}
          <Transformer ref={transformerRef} />
        </Layer>
      </Stage>

      {/* 右键菜单 */}
      <ContextMenu
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        items={createCanvasContextMenu(
          selectedElements.length > 0,
          hasClipboard,
          elements.filter(el => selectedElements.includes(el.id)),
          handleCopy,
          handleCut,
          handlePaste,
          () => {}, // handleDelete - 需要实现
          () => {}, // handleBringToFront
          () => {}, // handleSendToBack
          () => {}, // handleGroup
          () => {}, // handleUngroup
          () => {}, // handleLock
          () => {}, // handleUnlock
          () => {}, // handleHide
          () => {}  // handleShow
        )}
        onClose={() => setContextMenu(prev => ({ ...prev, visible: false }))}
      />
    </div>
  );
});
