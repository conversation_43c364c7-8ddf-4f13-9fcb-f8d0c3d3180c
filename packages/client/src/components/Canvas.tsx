import React, { forwardRef, useRef, useCallback, useState, useEffect } from 'react';
import { Stage, Layer, Rect, Circle, Line, Text, Transformer } from 'react-konva';
import Konva from 'konva';
import { v4 as uuidv4 } from 'uuid';

interface CanvasElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text';
  x: number;
  y: number;
  width?: number;
  height?: number;
  radius?: number;
  points?: number[];
  text?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  visible?: boolean;
  locked?: boolean;
  rotation?: number;
  fontSize?: number;
  fontFamily?: string;
}

interface CanvasProps {
  selectedTool: string;
  elements: CanvasElement[];
  selectedElements: string[];
  onElementSelect: (elementIds: string[]) => void;
  onElementAdd: (element: CanvasElement) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
  onToolChange: (tool: string) => void;
  scale: number;
  position: { x: number; y: number };
  onScaleChange: (scale: number) => void;
  onPositionChange: (position: { x: number; y: number }) => void;
}

export const Canvas = forwardRef<Konva.Stage, CanvasProps>(({
  selectedTool,
  elements,
  selectedElements,
  onElementSelect,
  onElementAdd,
  onElementUpdate,
  onToolChange,
  scale,
  position,
  onScaleChange,
  onPositionChange,
}, ref) => {
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDrawing, setCurrentDrawing] = useState<CanvasElement | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionRect, setSelectionRect] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastPointerPosition, setLastPointerPosition] = useState<{ x: number; y: number } | null>(null);
  const [stageSize, setStageSize] = useState({
    width: window.innerWidth - 576, // 减去左右面板宽度 (288 * 2)
    height: window.innerHeight - 80  // 减去工具栏高度
  });

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setStageSize({
        width: window.innerWidth - 576, // 减去左右面板宽度
        height: window.innerHeight - 80  // 减去工具栏高度
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 处理滚轮缩放
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();

    const stage = e.target.getStage();
    if (!stage) return;

    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    // 计算新的缩放比例
    const direction = e.evt.deltaY > 0 ? -1 : 1;
    const factor = 1.1;
    const newScale = direction > 0 ? oldScale * factor : oldScale / factor;

    // 限制缩放范围
    const clampedScale = Math.max(0.1, Math.min(5, newScale));

    const newPos = {
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    };

    onScaleChange(clampedScale);
    onPositionChange(newPos);
  }, [onScaleChange, onPositionChange]);

  // 处理鼠标按下
  const handleMouseDown = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const pos = stage.getPointerPosition();
    if (!pos) return;

    // 如果按住中键，开始平移
    if (e.evt.button === 1) {
      setIsPanning(true);
      setLastPointerPosition(pos);
      return;
    }

    // 如果按下的是舞台背景
    if (e.target === stage) {
      if (selectedTool === 'select') {
        // 如果不是按住Ctrl/Cmd，清除当前选择
        if (!e.evt.ctrlKey && !e.evt.metaKey) {
          onElementSelect([]);
        }

        // 开始框选
        setIsSelecting(true);
        setSelectionRect({
          x: pos.x,
          y: pos.y,
          width: 0,
          height: 0,
        });
      } else {
        // 立即开始绘制新元素
        handleStartDrawing(pos);
      }
      return;
    }

    // 如果点击的是元素
    const clickedElement = e.target;
    const elementId = clickedElement.id();
    
    if (selectedTool === 'select' && elementId) {
      // 处理元素选择
      if (e.evt.ctrlKey || e.evt.metaKey) {
        // 多选
        const newSelection = selectedElements.includes(elementId)
          ? selectedElements.filter(id => id !== elementId)
          : [...selectedElements, elementId];
        onElementSelect(newSelection);
      } else {
        // 单选
        onElementSelect([elementId]);
      }
    }
  }, [selectedTool, selectedElements, onElementSelect]);

  // 开始绘制
  const handleStartDrawing = useCallback((pos: { x: number; y: number }) => {
    if (selectedTool === 'select') return;

    const newElement: CanvasElement = {
      id: uuidv4(),
      type: selectedTool as any,
      x: pos.x,
      y: pos.y,
      fill: '#3b82f6',
      stroke: '#2563eb',
      strokeWidth: 2,
      visible: true,
      locked: false,
    };

    switch (selectedTool) {
      case 'rectangle':
        newElement.width = 1; // 初始最小尺寸
        newElement.height = 1;
        break;
      case 'circle':
        newElement.radius = 1; // 初始最小半径
        break;
      case 'line':
        newElement.points = [0, 0, 0, 0];
        break;
      case 'text':
        newElement.text = '文本';
        newElement.width = 100;
        newElement.height = 30;
        newElement.fontSize = 16;
        newElement.fontFamily = 'Arial';
        newElement.fill = '#000000';
        onElementAdd(newElement);
        return;
    }

    // 立即添加元素到画布，这样就能在拖拽时看到实时效果
    onElementAdd(newElement);
    setCurrentDrawing(newElement);
    setIsDrawing(true);
  }, [selectedTool, onElementAdd]);

  // 绘制过程中
  const handleMouseMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const pos = stage.getPointerPosition();
    if (!pos) return;

    // 处理平移
    if (isPanning && lastPointerPosition) {
      const dx = pos.x - lastPointerPosition.x;
      const dy = pos.y - lastPointerPosition.y;

      const newPos = {
        x: position.x + dx,
        y: position.y + dy,
      };

      onPositionChange(newPos);
      setLastPointerPosition(pos);
      return;
    }

    // 处理框选
    if (isSelecting && selectionRect) {
      const newRect = {
        x: Math.min(pos.x, selectionRect.x),
        y: Math.min(pos.y, selectionRect.y),
        width: Math.abs(pos.x - selectionRect.x),
        height: Math.abs(pos.y - selectionRect.y),
      };
      setSelectionRect(newRect);
      return;
    }

    // 处理绘制
    if (!isDrawing || !currentDrawing) return;

    const updatedElement = { ...currentDrawing };
    const deltaX = pos.x - currentDrawing.x;
    const deltaY = pos.y - currentDrawing.y;

    switch (currentDrawing.type) {
      case 'rectangle':
        updatedElement.width = Math.abs(deltaX);
        updatedElement.height = Math.abs(deltaY);
        if (deltaX < 0) updatedElement.x = pos.x;
        if (deltaY < 0) updatedElement.y = pos.y;
        break;
      case 'circle':
        updatedElement.radius = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        break;
      case 'line':
        updatedElement.points = [0, 0, deltaX, deltaY];
        break;
    }

    // 实时更新画布上的元素
    setCurrentDrawing(updatedElement);
    onElementUpdate(updatedElement.id, updatedElement);
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, lastPointerPosition, position, onElementUpdate, onPositionChange]);

  // 结束绘制或框选
  const handleMouseUp = useCallback(() => {
    // 处理平移结束
    if (isPanning) {
      setIsPanning(false);
      setLastPointerPosition(null);
      return;
    }

    // 处理框选完成
    if (isSelecting && selectionRect) {
      // 找到框选区域内的所有元素
      const selectedIds: string[] = [];

      elements.forEach(element => {
        // 检查元素是否在框选区域内
        const elementBounds = {
          x: element.x,
          y: element.y,
          width: element.width || (element.radius ? element.radius * 2 : 0),
          height: element.height || (element.radius ? element.radius * 2 : 0),
        };

        // 简单的矩形碰撞检测
        if (
          elementBounds.x < selectionRect.x + selectionRect.width &&
          elementBounds.x + elementBounds.width > selectionRect.x &&
          elementBounds.y < selectionRect.y + selectionRect.height &&
          elementBounds.y + elementBounds.height > selectionRect.y
        ) {
          selectedIds.push(element.id);
        }
      });

      // 更新选择
      onElementSelect(selectedIds);

      // 清理框选状态
      setIsSelecting(false);
      setSelectionRect(null);
      return;
    }

    // 处理绘制完成
    if (isDrawing && currentDrawing) {
      // 绘制完成，清理状态
      setIsDrawing(false);
      setCurrentDrawing(null);

      // 选择刚创建的元素
      onElementSelect([currentDrawing.id]);

      // 自动切换到选择工具
      onToolChange('select');
    }
  }, [isDrawing, currentDrawing, isSelecting, selectionRect, isPanning, elements, onElementSelect, onToolChange]);

  // 处理元素变换
  const handleTransformEnd = useCallback((e: Konva.KonvaEventObject<Event>) => {
    const node = e.target;
    const elementId = node.id();
    
    if (elementId) {
      const updates: Partial<CanvasElement> = {
        x: node.x(),
        y: node.y(),
        rotation: node.rotation(),
      };

      if (node.width) updates.width = node.width() * node.scaleX();
      if (node.height) updates.height = node.height() * node.scaleY();
      if ('radius' in node && typeof (node as any).radius === 'function') {
        updates.radius = (node as any).radius() * node.scaleX();
      }

      // 重置缩放
      node.scaleX(1);
      node.scaleY(1);

      onElementUpdate(elementId, updates);
    }
  }, [onElementUpdate]);

  // 渲染元素
  const renderElement = useCallback((element: CanvasElement) => {
    const commonProps = {
      key: element.id,
      id: element.id,
      x: element.x,
      y: element.y,
      fill: element.fill,
      stroke: element.stroke,
      strokeWidth: element.strokeWidth,
      draggable: selectedTool === 'select',
      onTransformEnd: handleTransformEnd,
    };

    switch (element.type) {
      case 'rectangle':
        return (
          <Rect
            {...commonProps}
            width={element.width || 0}
            height={element.height || 0}
          />
        );
      case 'circle':
        return (
          <Circle
            {...commonProps}
            radius={element.radius || 0}
          />
        );
      case 'line':
        return (
          <Line
            {...commonProps}
            points={element.points || [0, 0, 0, 0]}
          />
        );
      case 'text':
        return (
          <Text
            {...commonProps}
            text={element.text || ''}
            fontSize={16}
            width={element.width}
            height={element.height}
          />
        );
      default:
        return null;
    }
  }, [selectedTool, handleTransformEnd]);

  // 更新变换器
  React.useEffect(() => {
    if (transformerRef.current && stageRef.current) {
      const transformer = transformerRef.current;
      const stage = stageRef.current;
      
      if (selectedElements.length > 0) {
        const nodes = selectedElements.map(id => stage.findOne(`#${id}`)).filter(Boolean) as Konva.Node[];
        transformer.nodes(nodes);
      } else {
        transformer.nodes([]);
      }
      
      transformer.getLayer()?.batchDraw();
    }
  }, [selectedElements]);

  return (
    <div className="w-full h-full bg-gray-100 relative overflow-hidden">
      {/* 画布背景网格 */}
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />

      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={scale}
        scaleY={scale}
        x={position.x}
        y={position.y}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
      >
        <Layer>
          {/* 渲染所有元素 */}
          {elements.map(renderElement)}

          {/* 框选矩形 */}
          {selectionRect && (
            <Rect
              x={selectionRect.x}
              y={selectionRect.y}
              width={selectionRect.width}
              height={selectionRect.height}
              fill="rgba(59, 130, 246, 0.1)"
              stroke="#3b82f6"
              strokeWidth={1}
              dash={[5, 5]}
            />
          )}

          {/* 变换器 */}
          <Transformer ref={transformerRef} />
        </Layer>
      </Stage>
    </div>
  );
});
