import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Divider,
} from '@mui/material';
import {
  CropSquare as RectangleIcon,
  RadioButtonUnchecked as CircleIcon,
  Remove as LineIcon,
  TextFields as TextIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

interface LayerElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text';
  text?: string;
  visible?: boolean;
  locked?: boolean;
}

interface LayerPanelProps {
  elements: LayerElement[];
  selectedElements: string[];
  onElementSelect: (elementIds: string[]) => void;
  onElementUpdate: (elementId: string, updates: Partial<LayerElement>) => void;
  onElementDelete: (elementIds: string[]) => void;
}

export const LayerPanel: React.FC<LayerPanelProps> = ({
  elements,
  selectedElements,
  onElementSelect,
  onElementUpdate,
  onElementDelete,
}) => {
  const getElementIcon = (type: string) => {
    switch (type) {
      case 'rectangle':
        return <RectangleIcon fontSize="small" />;
      case 'circle':
        return <CircleIcon fontSize="small" />;
      case 'line':
        return <LineIcon fontSize="small" />;
      case 'text':
        return <TextIcon fontSize="small" />;
      default:
        return <RectangleIcon fontSize="small" />;
    }
  };

  const getElementName = (element: LayerElement) => {
    switch (element.type) {
      case 'rectangle':
        return '矩形';
      case 'circle':
        return '圆形';
      case 'line':
        return '线条';
      case 'text':
        return element.text || '文本';
      default:
        return '元素';
    }
  };

  const handleElementClick = (elementId: string, event: React.MouseEvent) => {
    if (event.ctrlKey || event.metaKey) {
      // 多选
      const newSelection = selectedElements.includes(elementId)
        ? selectedElements.filter(id => id !== elementId)
        : [...selectedElements, elementId];
      onElementSelect(newSelection);
    } else {
      // 单选
      onElementSelect([elementId]);
    }
  };

  const handleVisibilityToggle = (elementId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const element = elements.find(el => el.id === elementId);
    if (element) {
      onElementUpdate(elementId, { visible: !element.visible });
    }
  };

  const handleLockToggle = (elementId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const element = elements.find(el => el.id === elementId);
    if (element) {
      onElementUpdate(elementId, { locked: !element.locked });
    }
  };

  const handleDelete = (elementId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onElementDelete([elementId]);
  };

  return (
    <Box
      sx={{
        width: 280,
        height: '100%',
        bgcolor: 'white',
        borderRight: '1px solid #e8eaed',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 标题 */}
      <Box sx={{ p: 2, borderBottom: '1px solid #e8eaed' }}>
        <Typography variant="h6" component="h2" sx={{ fontWeight: 500, color: '#202124' }}>
          图层
        </Typography>
      </Box>

      {/* 图层列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {elements.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: '#5f6368', mb: 1 }}>
              暂无图层
            </Typography>
            <Typography variant="caption" sx={{ color: '#80868b' }}>
              使用工具栏中的绘图工具创建元素
            </Typography>
          </Box>
        ) : (
          <List dense>
            {elements.map((element) => (
              <ListItem
                key={element.id}
                disablePadding
                sx={{
                  bgcolor: selectedElements.includes(element.id)
                    ? '#e8f0fe'
                    : 'transparent',
                  borderRadius: 1,
                  mx: 1,
                  mb: 0.5,
                }}
              >
                <ListItemButton
                  onClick={(e) => handleElementClick(element.id, e)}
                  sx={{
                    py: 1,
                    px: 1.5,
                    borderRadius: 1,
                    opacity: element.visible === false ? 0.5 : 1,
                    '&:hover': {
                      bgcolor: selectedElements.includes(element.id)
                        ? '#e8f0fe'
                        : '#f8f9fa'
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {getElementIcon(element.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={getElementName(element)}
                    primaryTypographyProps={{
                      variant: 'body2',
                      noWrap: true,
                    }}
                  />
                  
                  {/* 操作按钮 */}
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton
                      size="small"
                      onClick={(e) => handleVisibilityToggle(element.id, e)}
                      sx={{ p: 0.25 }}
                    >
                      {element.visible === false ? (
                        <VisibilityOffIcon fontSize="small" />
                      ) : (
                        <VisibilityIcon fontSize="small" />
                      )}
                    </IconButton>
                    
                    <IconButton
                      size="small"
                      onClick={(e) => handleLockToggle(element.id, e)}
                      sx={{ p: 0.25 }}
                    >
                      {element.locked ? (
                        <LockIcon fontSize="small" />
                      ) : (
                        <LockOpenIcon fontSize="small" />
                      )}
                    </IconButton>
                    
                    <IconButton
                      size="small"
                      onClick={(e) => handleDelete(element.id, e)}
                      sx={{ p: 0.25 }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};
