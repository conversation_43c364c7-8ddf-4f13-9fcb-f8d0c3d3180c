import React from 'react';
import {
  Box,
  ToggleButton,
  ToggleButtonGroup,
  IconButton,
  Divider,
  Toolt<PERSON>,
} from '@mui/material';
import {
  NearMe as SelectIcon,
  CropSquare as RectangleIcon,
  RadioButtonUnchecked as CircleIcon,
  Remove as LineIcon,
  TextFields as TextIcon,
  Delete as DeleteIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as FitToScreenIcon,
} from '@mui/icons-material';

interface ToolbarProps {
  selectedTool: string;
  onToolChange: (tool: string) => void;
  onElementDelete: () => void;
  hasSelection: boolean;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  selectedTool,
  onToolChange,
  onElementDelete,
  hasSelection,
}) => {
  const handleToolChange = (
    event: React.MouseEvent<HTMLElement>,
    newTool: string | null,
  ) => {
    if (newTool !== null) {
      onToolChange(newTool);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        py: 1.5,
        gap: 2,
      }}
    >
      {/* 选择和绘图工具 */}
      <ToggleButtonGroup
        value={selectedTool}
        exclusive
        onChange={handleToolChange}
        size="small"
        sx={{
          '& .MuiToggleButton-root': {
            border: '1px solid #dadce0',
            borderRadius: 2,
            mx: 0.25,
            '&.Mui-selected': {
              bgcolor: '#e8f0fe',
              color: '#1a73e8',
              borderColor: '#1a73e8',
              '&:hover': {
                bgcolor: '#e8f0fe',
              }
            },
            '&:hover': {
              bgcolor: '#f8f9fa',
            }
          }
        }}
      >
        <ToggleButton value="select">
          <Tooltip title="选择工具 (V)">
            <SelectIcon fontSize="small" />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="rectangle">
          <Tooltip title="矩形工具 (R)">
            <RectangleIcon fontSize="small" />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="circle">
          <Tooltip title="圆形工具 (O)">
            <CircleIcon fontSize="small" />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="line">
          <Tooltip title="线条工具 (L)">
            <LineIcon fontSize="small" />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="text">
          <Tooltip title="文本工具 (T)">
            <TextIcon fontSize="small" />
          </Tooltip>
        </ToggleButton>
      </ToggleButtonGroup>

      <Divider orientation="vertical" flexItem sx={{ mx: 1, bgcolor: '#dadce0' }} />

      {/* 编辑操作 */}
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        <Tooltip title="撤销 (Ctrl+Z)">
          <IconButton
            size="small"
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <UndoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="重做 (Ctrl+Y)">
          <IconButton
            size="small"
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <RedoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="删除 (Delete)">
          <IconButton
            size="small"
            onClick={onElementDelete}
            disabled={!hasSelection}
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' },
              '&.Mui-disabled': { color: '#dadce0' }
            }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Divider orientation="vertical" flexItem sx={{ mx: 1, bgcolor: '#dadce0' }} />

      {/* 视图控制 */}
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        <Tooltip title="放大 (Ctrl++)">
          <IconButton
            size="small"
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <ZoomInIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="缩小 (Ctrl+-)">
          <IconButton
            size="small"
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <ZoomOutIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title="适应屏幕 (Ctrl+0)">
          <IconButton
            size="small"
            sx={{
              borderRadius: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <FitToScreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};
