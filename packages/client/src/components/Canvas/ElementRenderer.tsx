import React, { useCallback } from 'react';
import { Rect, Circle, Line, Text, Image } from 'react-konva';
import { CanvasElement } from '../../types/canvas';
import { CANVAS_CONFIG } from '../../config/canvas';

interface ElementRendererProps {
  element: CanvasElement;
  selectedTool: string;
  imageCache: Map<string, HTMLImageElement>;
  onTransformEnd: (elementId: string, updates: Partial<CanvasElement>) => void;
  onElementUpdate: (elementId: string, updates: Partial<CanvasElement>) => void;
  loadImage: (src: string) => Promise<HTMLImageElement>;
}

export const ElementRenderer: React.FC<ElementRendererProps> = React.memo(({
  element,
  selectedTool,
  imageCache,
  onTransformEnd,
  onElementUpdate,
  loadImage,
}) => {
  const commonProps = {
    key: element.id,
    id: element.id,
    x: element.x,
    y: element.y,
    fill: element.fill,
    stroke: element.stroke,
    strokeWidth: element.strokeWidth,
    draggable: selectedTool === 'select',
    onTransformEnd: () => onTransformEnd(element.id, {}),
  };

  const renderElement = useCallback(() => {
    switch (element.type) {
      case 'rectangle':
        return (
          <Rect
            {...commonProps}
            width={element.width || 0}
            height={element.height || 0}
          />
        );
      
      case 'circle':
        return (
          <Circle
            {...commonProps}
            radius={element.radius || 0}
          />
        );
      
      case 'line':
        return (
          <Line
            {...commonProps}
            points={element.points || [0, 0, 0, 0]}
          />
        );
      
      case 'text':
        return (
          <Text
            {...commonProps}
            text={element.text || ''}
            fontSize={element.fontSize || CANVAS_CONFIG.TEXT_DEFAULTS.fontSize}
            fontFamily={element.fontFamily || CANVAS_CONFIG.TEXT_DEFAULTS.fontFamily}
            width={element.width}
            height={element.height}
          />
        );
      
      case 'image':
        const cachedImage = imageCache.get(element.src || '');
        if (cachedImage) {
          return (
            <Image
              {...commonProps}
              image={cachedImage}
              width={element.width || cachedImage.width}
              height={element.height || cachedImage.height}
            />
          );
        } else if (element.src) {
          // 异步加载图片
          loadImage(element.src).then(() => {
            // 触发重新渲染
            onElementUpdate(element.id, {});
          });
          
          // 显示占位符
          return (
            <Rect
              {...commonProps}
              width={element.width || CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.width}
              height={element.height || CANVAS_CONFIG.DEFAULT_ELEMENT_SIZE.height}
              fill={CANVAS_CONFIG.COLORS.placeholder}
              stroke={CANVAS_CONFIG.COLORS.placeholderStroke}
              strokeWidth={1}
            />
          );
        }
        return null;
      
      default:
        return null;
    }
  }, [element, commonProps, imageCache, loadImage, onElementUpdate]);

  return renderElement();
});
