import React from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Paper,
  Grid,
} from '@mui/material';

interface PropertiesElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text';
  x: number;
  y: number;
  width?: number;
  height?: number;
  radius?: number;
  text?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  rotation?: number;
}

interface PropertiesPanelProps {
  selectedElements: PropertiesElement[];
  onElementUpdate: (elementId: string, updates: Partial<PropertiesElement>) => void;
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedElements,
  onElementUpdate,
}) => {
  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null;
  const hasMultipleSelection = selectedElements.length > 1;

  const handlePropertyChange = (property: string, value: any) => {
    if (selectedElement) {
      onElementUpdate(selectedElement.id, { [property]: value });
    } else if (hasMultipleSelection) {
      // 批量更新多个元素
      selectedElements.forEach(element => {
        onElementUpdate(element.id, { [property]: value });
      });
    }
  };

  const renderPositionProperties = () => (
    <Paper sx={{
      p: 2,
      mb: 2,
      border: '1px solid #e8eaed',
      borderRadius: 2,
      boxShadow: 'none'
    }}>
      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500, color: '#202124' }}>
        位置和大小
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            label="X"
            type="number"
            size="small"
            fullWidth
            value={selectedElement?.x || 0}
            onChange={(e) => handlePropertyChange('x', parseFloat(e.target.value) || 0)}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            label="Y"
            type="number"
            size="small"
            fullWidth
            value={selectedElement?.y || 0}
            onChange={(e) => handlePropertyChange('y', parseFloat(e.target.value) || 0)}
          />
        </Grid>
        
        {selectedElement?.type === 'rectangle' && (
          <>
            <Grid item xs={6}>
              <TextField
                label="宽度"
                type="number"
                size="small"
                fullWidth
                value={selectedElement?.width || 0}
                onChange={(e) => handlePropertyChange('width', parseFloat(e.target.value) || 0)}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="高度"
                type="number"
                size="small"
                fullWidth
                value={selectedElement?.height || 0}
                onChange={(e) => handlePropertyChange('height', parseFloat(e.target.value) || 0)}
              />
            </Grid>
          </>
        )}
        
        {selectedElement?.type === 'circle' && (
          <Grid item xs={12}>
            <TextField
              label="半径"
              type="number"
              size="small"
              fullWidth
              value={selectedElement?.radius || 0}
              onChange={(e) => handlePropertyChange('radius', parseFloat(e.target.value) || 0)}
            />
          </Grid>
        )}
        
        <Grid item xs={12}>
          <Typography variant="body2" gutterBottom>
            旋转: {Math.round(selectedElement?.rotation || 0)}°
          </Typography>
          <Slider
            value={selectedElement?.rotation || 0}
            onChange={(_, value) => handlePropertyChange('rotation', value)}
            min={-180}
            max={180}
            step={1}
            size="small"
          />
        </Grid>
      </Grid>
    </Paper>
  );

  const renderAppearanceProperties = () => (
    <Paper sx={{
      p: 2,
      mb: 2,
      border: '1px solid #e8eaed',
      borderRadius: 2,
      boxShadow: 'none'
    }}>
      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500, color: '#202124' }}>
        外观
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            label="填充颜色"
            type="color"
            size="small"
            fullWidth
            value={selectedElement?.fill || '#3f51b5'}
            onChange={(e) => handlePropertyChange('fill', e.target.value)}
            InputProps={{
              sx: { height: 40 }
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            label="边框颜色"
            type="color"
            size="small"
            fullWidth
            value={selectedElement?.stroke || '#1976d2'}
            onChange={(e) => handlePropertyChange('stroke', e.target.value)}
            InputProps={{
              sx: { height: 40 }
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Typography variant="body2" gutterBottom>
            边框宽度: {selectedElement?.strokeWidth || 2}px
          </Typography>
          <Slider
            value={selectedElement?.strokeWidth || 2}
            onChange={(_, value) => handlePropertyChange('strokeWidth', value)}
            min={0}
            max={20}
            step={1}
            size="small"
          />
        </Grid>
      </Grid>
    </Paper>
  );

  const renderTextProperties = () => (
    selectedElement?.type === 'text' && (
      <Paper sx={{
        p: 2,
        mb: 2,
        border: '1px solid #e8eaed',
        borderRadius: 2,
        boxShadow: 'none'
      }}>
        <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500, color: '#202124' }}>
          文本
        </Typography>
        <TextField
          label="文本内容"
          multiline
          rows={3}
          size="small"
          fullWidth
          value={selectedElement?.text || ''}
          onChange={(e) => handlePropertyChange('text', e.target.value)}
        />
      </Paper>
    )
  );

  return (
    <Box
      sx={{
        width: 280,
        height: '100%',
        bgcolor: 'white',
        borderLeft: '1px solid #e8eaed',
        overflow: 'auto',
      }}
    >
      {/* 标题 */}
      <Box sx={{ p: 2, borderBottom: '1px solid #e8eaed' }}>
        <Typography variant="h6" component="h2" sx={{ fontWeight: 500, color: '#202124' }}>
          属性
        </Typography>
      </Box>

      {/* 属性内容 */}
      <Box sx={{ p: 2 }}>
        {selectedElements.length === 0 && (
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Typography variant="body2" color="text.secondary">
              选择一个元素来编辑属性
            </Typography>
          </Box>
        )}

        {hasMultipleSelection && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              已选择 {selectedElements.length} 个元素
            </Typography>
            <Divider sx={{ my: 1 }} />
          </Box>
        )}

        {selectedElement && (
          <>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {selectedElement.type === 'rectangle' && '矩形'}
                {selectedElement.type === 'circle' && '圆形'}
                {selectedElement.type === 'line' && '线条'}
                {selectedElement.type === 'text' && '文本'}
              </Typography>
            </Box>
            
            {renderPositionProperties()}
            {renderAppearanceProperties()}
            {renderTextProperties()}
          </>
        )}

        {hasMultipleSelection && (
          <>
            {renderAppearanceProperties()}
          </>
        )}
      </Box>
    </Box>
  );
};
