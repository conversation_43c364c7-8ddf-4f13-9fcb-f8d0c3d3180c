import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  ArrowLeft,
  Save,
  Share2,
  MousePointer,
  Square,
  Circle,
  Minus,
  Type,
  Image as ImageIcon,
  Undo,
  Redo,
  Trash2,
  ZoomIn,
  ZoomOut,
  Maximize,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-react';
import Konva from 'konva';

import { Canvas } from '../components/Canvas';

export const DesignEditor: React.FC = () => {
  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId: string }>();
  const stageRef = useRef<Konva.Stage>(null);
  
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  const [canvasElements, setCanvasElements] = useState<any[]>([]);
  const [stageScale, setStageScale] = useState(1);
  const [stagePosition, setStagePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // 初始化项目数据
    console.log('Loading project:', projectId);
  }, [projectId]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleSave = () => {
    // TODO: 实现保存功能
    console.log('Saving project...');
  };

  const handleShare = () => {
    // TODO: 实现分享功能
    console.log('Sharing project...');
  };

  const handleToolChange = (tool: string) => {
    setSelectedTool(tool);
  };

  const handleElementSelect = (elementIds: string[]) => {
    setSelectedElements(elementIds);
  };

  const handleElementAdd = (element: any) => {
    setCanvasElements(prev => [...prev, element]);
  };

  const handleElementUpdate = (elementId: string, updates: any) => {
    setCanvasElements(prev => 
      prev.map(el => el.id === elementId ? { ...el, ...updates } : el)
    );
  };

  const handleElementDelete = (elementIds: string[]) => {
    setCanvasElements(prev =>
      prev.filter(el => !elementIds.includes(el.id))
    );
    setSelectedElements([]);
  };

  const handleZoomIn = () => {
    const newScale = Math.min(stageScale * 1.2, 5);
    setStageScale(newScale);
  };

  const handleZoomOut = () => {
    const newScale = Math.max(stageScale / 1.2, 0.1);
    setStageScale(newScale);
  };

  const handleFitToScreen = () => {
    setStageScale(1);
    setStagePosition({ x: 0, y: 0 });
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">

      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 返回按钮和项目名称 */}
            <div className="flex items-center space-x-3">
              <button
                onClick={handleBackToHome}
                className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
                title="返回首页"
              >
                <ArrowLeft className="w-4 h-4 text-gray-600" />
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-xs">N</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {projectId ? `${projectId}` : '未命名设计'}
                </span>
              </div>
            </div>

            <div className="w-px h-6 bg-gray-300" />

            {/* 选择和绘图工具 */}
            <div className="flex items-center space-x-1">
              {[
                { tool: 'select', icon: MousePointer, label: '选择工具 (V)' },
                { tool: 'rectangle', icon: Square, label: '矩形工具 (R)' },
                { tool: 'circle', icon: Circle, label: '圆形工具 (O)' },
                { tool: 'line', icon: Minus, label: '线条工具 (L)' },
                { tool: 'text', icon: Type, label: '文本工具 (T)' },
                { tool: 'image', icon: ImageIcon, label: '图片工具 (I)' },
              ].map(({ tool, icon: Icon, label }) => (
                <button
                  key={tool}
                  onClick={() => handleToolChange(tool)}
                  className={`toolbar-button ${selectedTool === tool ? 'active' : ''}`}
                  title={label}
                >
                  <Icon className="w-4 h-4" />
                </button>
              ))}
            </div>

            <div className="w-px h-6 bg-gray-300" />

            {/* 编辑操作 */}
            <div className="flex items-center space-x-1">
              <button className="toolbar-button" title="撤销 (Ctrl+Z)">
                <Undo className="w-4 h-4" />
              </button>
              <button className="toolbar-button" title="重做 (Ctrl+Y)">
                <Redo className="w-4 h-4" />
              </button>
              <button
                className={`toolbar-button ${selectedElements.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => handleElementDelete(selectedElements)}
                disabled={selectedElements.length === 0}
                title="删除 (Delete)"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>

            <div className="w-px h-6 bg-gray-300" />

            {/* 视图控制 */}
            <div className="flex items-center space-x-1">
              <button
                className="toolbar-button"
                onClick={handleZoomIn}
                title="放大 (Ctrl++)"
              >
                <ZoomIn className="w-4 h-4" />
              </button>
              <button
                className="toolbar-button"
                onClick={handleZoomOut}
                title="缩小 (Ctrl+-)"
              >
                <ZoomOut className="w-4 h-4" />
              </button>
              <button
                className="toolbar-button"
                onClick={handleFitToScreen}
                title="适应屏幕 (Ctrl+0)"
              >
                <Maximize className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* 右侧操作按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSave}
              className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
              title="保存 (Ctrl+S)"
            >
              <Save className="w-4 h-4 text-gray-600" />
            </button>
            <button
              onClick={handleShare}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2"
              title="分享项目"
            >
              <Share2 className="w-4 h-4" />
              <span>分享</span>
            </button>
          </div>
        </div>
      </div>

      {/* 主要工作区域 */}
      <div className="flex-1 flex overflow-hidden h-full">
        {/* 左侧图层面板 */}
        <div className="w-72 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">图层</h2>
          </div>
          <div className="flex-1 overflow-auto p-2">
            {canvasElements.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-sm text-gray-500 mb-1">暂无图层</p>
                <p className="text-xs text-gray-400">使用工具栏中的绘图工具创建元素</p>
              </div>
            ) : (
              <div className="space-y-1">
                {canvasElements.map((element) => (
                  <div
                    key={element.id}
                    onClick={(e) => {
                      if (e.ctrlKey || e.metaKey) {
                        const newSelection = selectedElements.includes(element.id)
                          ? selectedElements.filter(id => id !== element.id)
                          : [...selectedElements, element.id];
                        handleElementSelect(newSelection);
                      } else {
                        handleElementSelect([element.id]);
                      }
                    }}
                    className={`flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-colors ${
                      selectedElements.includes(element.id)
                        ? 'bg-blue-100 text-blue-900'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                      {element.type === 'rectangle' && <Square className="w-4 h-4 text-gray-600" />}
                      {element.type === 'circle' && <Circle className="w-4 h-4 text-gray-600" />}
                      {element.type === 'line' && <Minus className="w-4 h-4 text-gray-600" />}
                      {element.type === 'text' && <Type className="w-4 h-4 text-gray-600" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {element.type === 'text' ? element.text || '文本' :
                         element.type === 'rectangle' ? '矩形' :
                         element.type === 'circle' ? '圆形' : '线条'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-200">
                        {element.visible === false ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-200">
                        {element.locked ? <Lock className="w-3 h-3" /> : <Unlock className="w-3 h-3" />}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 中间画布区域 */}
        <div className="flex-1 relative overflow-hidden">
          <Canvas
            ref={stageRef}
            selectedTool={selectedTool}
            elements={canvasElements}
            selectedElements={selectedElements}
            onElementSelect={handleElementSelect}
            onElementAdd={handleElementAdd}
            onElementUpdate={handleElementUpdate}
            onToolChange={handleToolChange}
            scale={stageScale}
            position={stagePosition}
            onScaleChange={setStageScale}
            onPositionChange={setStagePosition}
          />
        </div>

        {/* 右侧属性面板 */}
        <div className="w-72 bg-white border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">属性</h2>
          </div>
          <div className="flex-1 overflow-auto p-4">
            {selectedElements.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-sm text-gray-500">选择一个元素来编辑属性</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* 这里可以添加属性编辑控件 */}
                <div className="bg-gray-50 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">位置和大小</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">X</label>
                      <input type="number" className="w-full px-2 py-1 text-xs border border-gray-300 rounded" />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">Y</label>
                      <input type="number" className="w-full px-2 py-1 text-xs border border-gray-300 rounded" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
