import React, { useState, useRef, useEffect } from 'react';
import { Box, AppBar, Toolbar, Typography, IconButton, Divider, Button } from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import Konva from 'konva';

import { Toolbar as DesignToolbar } from '../components/Toolbar';
import { LayerPanel } from '../components/LayerPanel';
import { PropertiesPanel } from '../components/PropertiesPanel';
import { Canvas } from '../components/Canvas';

export const DesignEditor: React.FC = () => {
  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId: string }>();
  const stageRef = useRef<Konva.Stage>(null);
  
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  const [canvasElements, setCanvasElements] = useState<any[]>([]);
  const [stageScale, setStageScale] = useState(1);
  const [stagePosition, setStagePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // 初始化项目数据
    console.log('Loading project:', projectId);
  }, [projectId]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleSave = () => {
    // TODO: 实现保存功能
    console.log('Saving project...');
  };

  const handleShare = () => {
    // TODO: 实现分享功能
    console.log('Sharing project...');
  };

  const handleToolChange = (tool: string) => {
    setSelectedTool(tool);
  };

  const handleElementSelect = (elementIds: string[]) => {
    setSelectedElements(elementIds);
  };

  const handleElementAdd = (element: any) => {
    setCanvasElements(prev => [...prev, element]);
  };

  const handleElementUpdate = (elementId: string, updates: any) => {
    setCanvasElements(prev => 
      prev.map(el => el.id === elementId ? { ...el, ...updates } : el)
    );
  };

  const handleElementDelete = (elementIds: string[]) => {
    setCanvasElements(prev => 
      prev.filter(el => !elementIds.includes(el.id))
    );
    setSelectedElements([]);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f8f9fa' }}>
      {/* 顶部导航栏 */}
      <AppBar position="static" elevation={0} sx={{
        bgcolor: 'white',
        color: 'text.primary',
        borderBottom: '1px solid #e8eaed'
      }}>
        <Toolbar sx={{ minHeight: '56px !important', px: 2 }}>
          <IconButton
            onClick={handleBackToHome}
            sx={{
              mr: 2,
              '&:hover': { bgcolor: '#f8f9fa' }
            }}
          >
            <ArrowBackIcon />
          </IconButton>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{
              width: 24,
              height: 24,
              bgcolor: '#4285f4',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>
              N
            </Box>
            <Typography variant="h6" component="div" sx={{ fontWeight: 500, color: '#202124' }}>
              {projectId ? `${projectId}` : '未命名设计'}
            </Typography>
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              onClick={handleSave}
              sx={{
                bgcolor: '#f8f9fa',
                '&:hover': { bgcolor: '#e8eaed' }
              }}
            >
              <SaveIcon />
            </IconButton>
            <Button
              variant="contained"
              onClick={handleShare}
              sx={{
                bgcolor: '#4285f4',
                '&:hover': { bgcolor: '#3367d6' },
                borderRadius: 2,
                px: 3
              }}
            >
              分享
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      {/* 工具栏 */}
      <Box sx={{
        bgcolor: 'white',
        borderBottom: '1px solid #e8eaed',
        px: 2
      }}>
        <DesignToolbar
          selectedTool={selectedTool}
          onToolChange={handleToolChange}
          onElementDelete={() => handleElementDelete(selectedElements)}
          hasSelection={selectedElements.length > 0}
        />
      </Box>

      {/* 主要工作区域 */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* 左侧图层面板 */}
        <LayerPanel
          elements={canvasElements}
          selectedElements={selectedElements}
          onElementSelect={handleElementSelect}
          onElementUpdate={handleElementUpdate}
          onElementDelete={handleElementDelete}
        />

        <Divider orientation="vertical" />

        {/* 中间画布区域 */}
        <Box sx={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
          <Canvas
            ref={stageRef}
            selectedTool={selectedTool}
            elements={canvasElements}
            selectedElements={selectedElements}
            onElementSelect={handleElementSelect}
            onElementAdd={handleElementAdd}
            onElementUpdate={handleElementUpdate}
            scale={stageScale}
            position={stagePosition}
            onScaleChange={setStageScale}
            onPositionChange={setStagePosition}
          />
        </Box>

        <Divider orientation="vertical" />

        {/* 右侧属性面板 */}
        <PropertiesPanel
          selectedElements={selectedElements.map(id => 
            canvasElements.find(el => el.id === id)
          ).filter(Boolean)}
          onElementUpdate={handleElementUpdate}
        />
      </Box>
    </Box>
  );
};
