import React from 'react';
import {
  Container,
  Typo<PERSON>,
  Button,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  AppBar,
  Toolbar,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  Folder as FolderIcon,
  AccountCircle as AccountCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

export const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const handleCreateProject = () => {
    // 生成新项目ID并导航到编辑器
    const projectId = `project-${Date.now()}`;
    navigate(`/editor/${projectId}`);
  };

  const recentProjects = [
    { id: '1', name: '移动应用设计', lastModified: '2小时前' },
    { id: '2', name: '网站首页设计', lastModified: '1天前' },
    { id: '3', name: 'Logo设计', lastModified: '3天前' },
  ];

  return (
    <Box sx={{ flexGrow: 1, bgcolor: '#f8f9fa', minHeight: '100vh' }}>
      {/* 顶部导航栏 */}
      <AppBar position="static" elevation={0} sx={{
        bgcolor: 'white',
        color: 'text.primary',
        borderBottom: '1px solid #e8eaed'
      }}>
        <Toolbar sx={{ px: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{
              width: 32,
              height: 32,
              bgcolor: '#4285f4',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}>
              N
            </Box>
            <Typography variant="h6" component="div" sx={{ fontWeight: 500, color: '#202124' }}>
              Nexus
            </Typography>
          </Box>
          <Box sx={{ flexGrow: 1 }} />
          <IconButton
            color="inherit"
            sx={{
              bgcolor: '#f8f9fa',
              '&:hover': { bgcolor: '#e8eaed' }
            }}
          >
            <AccountCircleIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* 左侧边栏和主内容区域 */}
        <Box sx={{ display: 'flex', gap: 3 }}>
          {/* 左侧边栏 */}
          <Box sx={{
            width: 280,
            bgcolor: 'white',
            borderRadius: 2,
            p: 2,
            height: 'fit-content',
            border: '1px solid #e8eaed'
          }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 500, color: '#202124' }}>
              最近使用
            </Typography>
            <Button
              variant="contained"
              fullWidth
              startIcon={<AddIcon />}
              onClick={handleCreateProject}
              sx={{
                mb: 3,
                bgcolor: '#4285f4',
                '&:hover': { bgcolor: '#3367d6' },
                borderRadius: 2,
                py: 1.5
              }}
            >
              创建新设计
            </Button>

            {/* 最近项目列表 */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {recentProjects.map((project) => (
                <Box
                  key={project.id}
                  onClick={() => navigate(`/editor/${project.id}`)}
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor: '#f8f9fa'
                    },
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      bgcolor: '#f1f3f4',
                      borderRadius: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <FolderIcon sx={{ fontSize: 20, color: '#5f6368' }} />
                  </Box>
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500, color: '#202124' }} noWrap>
                      {project.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {project.lastModified}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>

          {/* 主内容区域 */}
          <Box sx={{ flex: 1 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h5" sx={{ fontWeight: 500, color: '#202124', mb: 1 }}>
                开始新设计
              </Typography>
              <Typography variant="body2" color="text.secondary">
                选择模板或创建空白设计
              </Typography>
            </Box>

            {/* 模板网格 */}
            <Grid container spacing={2}>
              {/* 空白模板 */}
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: '2px dashed #dadce0',
                    bgcolor: 'transparent',
                    '&:hover': {
                      borderColor: '#4285f4',
                      bgcolor: '#f8f9fa'
                    },
                    height: 200
                  }}
                  onClick={handleCreateProject}
                >
                  <CardContent sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 2
                  }}>
                    <AddIcon sx={{ fontSize: 48, color: '#5f6368' }} />
                    <Typography variant="body2" color="text.secondary" textAlign="center">
                      空白设计
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* 预设模板 */}
              {[
                { name: '网页设计', size: '1920 × 1080', color: '#e3f2fd' },
                { name: '移动应用', size: '375 × 812', color: '#f3e5f5' },
                { name: '海报设计', size: '594 × 841', color: '#e8f5e8' },
                { name: '社交媒体', size: '1080 × 1080', color: '#fff3e0' },
                { name: '演示文稿', size: '1920 × 1080', color: '#fce4ec' },
                { name: '名片设计', size: '85 × 55', color: '#e0f2f1' },
                { name: 'Logo设计', size: '500 × 500', color: '#f1f8e9' },
                { name: '传单设计', size: '210 × 297', color: '#e8eaf6' },
              ].map((template, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px 0 rgba(60,64,67,.3)',
                        transition: 'all 0.2s ease-in-out'
                      },
                      height: 200
                    }}
                    onClick={handleCreateProject}
                  >
                    <CardContent sx={{ height: '100%', p: 0 }}>
                      <Box
                        sx={{
                          height: 140,
                          bgcolor: template.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative'
                        }}
                      >
                        <Box
                          sx={{
                            width: '60%',
                            height: '60%',
                            bgcolor: 'white',
                            borderRadius: 1,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                          }}
                        />
                      </Box>
                      <Box sx={{ p: 2 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, color: '#202124' }}>
                          {template.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {template.size}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>


      </Container>
    </Box>
  );
};
