import Konva from 'konva';
import { Point } from '../types/canvas';

/**
 * 获取相对于画布的鼠标位置（考虑缩放和平移）
 */
export const getRelativePointerPosition = (stage: Konva.Stage): Point | null => {
  const pointer = stage.getPointerPosition();
  if (!pointer) return null;
  
  // 转换为画布坐标系
  const transform = stage.getAbsoluteTransform().copy();
  transform.invert();
  return transform.point(pointer);
};

/**
 * 获取屏幕坐标位置
 */
export const getScreenPointerPosition = (stage: Konva.Stage): Point | null => {
  return stage.getPointerPosition();
};

/**
 * 计算两点之间的距离
 */
export const getDistance = (point1: Point, point2: Point): number => {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * 计算矩形边界
 */
export const getRectBounds = (x: number, y: number, width: number, height: number) => {
  return {
    x: Math.min(x, x + width),
    y: Math.min(y, y + height),
    width: Math.abs(width),
    height: Math.abs(height),
  };
};

/**
 * 检查点是否在矩形内
 */
export const isPointInRect = (
  point: Point,
  rect: { x: number; y: number; width: number; height: number }
): boolean => {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  );
};
