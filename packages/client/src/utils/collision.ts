import { CanvasElement, SelectionRect } from '../types/canvas';

/**
 * 检查元素是否在选择框内
 */
export const isElementInSelection = (
  element: CanvasElement,
  selectionRect: SelectionRect
): boolean => {
  // 计算元素边界
  const elementBounds = getElementBounds(element);
  
  // 简单的矩形碰撞检测
  return (
    elementBounds.x < selectionRect.x + selectionRect.width &&
    elementBounds.x + elementBounds.width > selectionRect.x &&
    elementBounds.y < selectionRect.y + selectionRect.height &&
    elementBounds.y + elementBounds.height > selectionRect.y
  );
};

/**
 * 获取元素的边界框
 */
export const getElementBounds = (element: CanvasElement) => {
  switch (element.type) {
    case 'rectangle':
    case 'text':
    case 'image':
      return {
        x: element.x,
        y: element.y,
        width: element.width || 0,
        height: element.height || 0,
      };
    case 'circle':
      const radius = element.radius || 0;
      return {
        x: element.x - radius,
        y: element.y - radius,
        width: radius * 2,
        height: radius * 2,
      };
    case 'line':
      const points = element.points || [0, 0, 0, 0];
      const minX = Math.min(points[0], points[2]);
      const maxX = Math.max(points[0], points[2]);
      const minY = Math.min(points[1], points[3]);
      const maxY = Math.max(points[1], points[3]);
      return {
        x: element.x + minX,
        y: element.y + minY,
        width: maxX - minX,
        height: maxY - minY,
      };
    default:
      return {
        x: element.x,
        y: element.y,
        width: 0,
        height: 0,
      };
  }
};

/**
 * 获取选择框内的所有元素ID
 */
export const getElementsInSelection = (
  elements: CanvasElement[],
  selectionRect: SelectionRect
): string[] => {
  return elements
    .filter(element => isElementInSelection(element, selectionRect))
    .map(element => element.id);
};
