{"name": "@koa/router", "description": "Router middleware for koa. Maintained by Forward Email and Lad.", "version": "13.1.0", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/koajs/router/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "@koajs"}, {"name": "<PERSON><PERSON>", "email": "imed-jabe<PERSON>@outlook.com"}], "dependencies": {"http-errors": "^2.0.0", "koa-compose": "^4.1.0", "path-to-regexp": "^6.3.0"}, "devDependencies": {"@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@ladjs/env": "^4.0.0", "eslint": "^8.39.0", "eslint-config-xo-lass": "^2.0.1", "fixpack": "^4.0.0", "husky": "^8.0.3", "jsdoc-to-markdown": "^8.0.0", "koa": "^2.15.3", "lint-staged": "^14.0.1", "mocha": "^10.7.3", "nyc": "^17.0.0", "remark-cli": "11", "remark-preset-github": "^4.0.4", "supertest": "^7.0.0", "xo": "0.53.1"}, "engines": {"node": ">= 18"}, "files": ["lib"], "homepage": "https://github.com/koajs/router", "keywords": ["koa", "middleware", "route", "router"], "license": "MIT", "main": "lib/router.js", "repository": {"type": "git", "url": "git+https://github.com/koajs/router.git"}, "scripts": {"bench": "make -C bench", "coverage": "nyc npm run test", "docs": "NODE_ENV=test jsdoc2md -t ./lib/API_tpl.hbs --src ./lib/*.js  >| API.md", "lint": "xo --fix && remark . -qfo && fixpack", "prepare": "husky install", "prextest": "npm run lint", "test": "mocha test/**/*.js", "test:watch": "mocha test/**/*.js --watch"}}