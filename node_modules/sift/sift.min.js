!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).sift={})}(this,(function(n){"use strict";var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])},t(n,r)};function r(n,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=n}t(n,r),n.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}"function"==typeof SuppressedError&&SuppressedError;var i=function(n){var t="[object "+n+"]";return function(n){return u(n)===t}},u=function(n){return Object.prototype.toString.call(n)},e=function(n){return n instanceof Date?n.getTime():o(n)?n.map(e):n&&"function"==typeof n.toJSON?n.toJSON():n},o=i("Array"),f=i("Object"),s=i("Function"),c=function(n,t){if(null==n&&n==t)return!0;if(n===t)return!0;if(Object.prototype.toString.call(n)!==Object.prototype.toString.call(t))return!1;if(o(n)){if(n.length!==t.length)return!1;for(var r=0,i=n.length;r<i;r++)if(!c(n[r],t[r]))return!1;return!0}if(f(n)){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(var u in n)if(!c(n[u],t[u]))return!1;return!0}return!1},h=function(n,t,r,i,u,e){var f=t[i];if(o(n)&&isNaN(Number(f))&&!function(n,t){return n.hasOwnProperty(t)&&!s(n[t])}(n,f))for(var c=0,a=n.length;c<a;c++)if(!h(n[c],t,r,i,c,n))return!1;return i===t.length||null==n?r(n,u,e,0===i,i===t.length):h(n[f],t,r,i+1,f,n)},a=function(){function n(n,t,r,i){this.params=n,this.owneryQuery=t,this.options=r,this.name=i,this.init()}return n.prototype.init=function(){},n.prototype.reset=function(){this.done=!1,this.keep=!1},n}(),l=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i)||this;return e.children=u,e}return r(t,n),t.prototype.reset=function(){this.keep=!1,this.done=!1;for(var n=0,t=this.children.length;n<t;n++)this.children[n].reset()},t.prototype.childrenNext=function(n,t,r,i,u){for(var e=!0,o=!0,f=0,s=this.children.length;f<s;f++){var c=this.children[f];if(c.done||c.next(n,t,r,i,u),c.keep||(o=!1),c.done){if(!c.keep)break}else e=!1}this.done=e,this.keep=o},t}(a),v=function(n){function t(t,r,i,u,e){var o=n.call(this,t,r,i,u)||this;return o.name=e,o}return r(t,n),t}(l),w=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(l),p=function(n){function t(t,r,i,u,e){var o=n.call(this,r,i,u,e)||this;return o.keyPath=t,o.propop=!0,o.t=function(n,t,r,i,u){return o.childrenNext(n,t,r,i,u),!o.done},o}return r(t,n),t.prototype.next=function(n,t,r){h(n,this.keyPath,this.t,0,t,r)},t}(l),b=function(n,t){if(n instanceof Function)return n;if(n instanceof RegExp)return function(t){var r="string"==typeof t&&n.test(t);return n.lastIndex=0,r};var r=e(n);return function(n){return t(r,e(n))}},y=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.next=function(n,t,r){Array.isArray(r)&&!r.hasOwnProperty(t)||this.i(n,t,r)&&(this.done=!0,this.keep=!0)},t}(a),d=function(n){return t=function(t,r,i,u){var o=typeof e(t),f=n(t);return new y((function(n){var t,r=null==(t=n)?null:t;return typeof e(r)===o&&f(r)}),r,i,u)},function(n,r,i,u){return t(n,r,i,u)};var t},$=function(n,t,r,i){var u=i.operations[n];return u||j(n),u(t,r,i,n)},j=function(n){throw new Error("Unsupported operation: ".concat(n))},m=function(n,t){for(var r in n)if(t.operations.hasOwnProperty(r)||"$"===r.charAt(0))return!0;return!1},O=function(n,t,r,i,u){if(m(t,u)){var e=E(t,r,u),o=e[0];if(e[1].length)throw new Error("Property queries must contain only operations, or exact objects.");return new p(n,t,i,u,o)}return new p(n,t,i,u,[new y(t,i,u)])},g=function(n,t,r){void 0===t&&(t=null);var i=void 0===r?{}:r,u=i.compare,e=i.operations,o={compare:u||c,operations:Object.assign({},e||{})},f=E(n,null,o),s=f[0],h=f[1],a=[];return s.length&&a.push(new p([],n,t,o,s)),a.push.apply(a,h),1===a.length?a[0]:new w(n,t,o,a)},E=function(n,t,r){var i,u=[],e=[];if(!(i=n)||i.constructor!==Object&&i.constructor!==Array&&"function Object() { [native code] }"!==i.constructor.toString()&&"function Array() { [native code] }"!==i.constructor.toString()||i.toJSON)return u.push(new y(n,n,r)),[u,e];for(var o in n)if(r.operations.hasOwnProperty(o)){var f=$(o,n[o],n,r);if(f&&!f.propop&&t&&!r.operations[t])throw new Error("Malformed query. ".concat(o," cannot be matched against property."));null!=f&&u.push(f)}else"$"===o.charAt(0)?j(o):e.push(O(o.split("."),n[o],o,n,r));return[u,e]},x=function(n){return function(t,r,i){return n.reset(),n.next(t,r,i),n.keep}},_=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.reset=function(){n.prototype.reset.call(this),this.keep=!0},t.prototype.next=function(n){this.i(n)&&(this.done=!0,this.keep=!1)},t}(a),A=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){if(!this.params||"object"!=typeof this.params)throw new Error("Malformed query. $elemMatch must by an object.");this.u=g(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){n.prototype.reset.call(this),this.u.reset()},t.prototype.next=function(n){if(o(n)){for(var t=0,r=n.length;t<r;t++){this.u.reset();var i=n[t];this.u.next(i,t,n,!1),this.keep=this.keep||this.u.keep}this.done=!0}else this.done=!1,this.keep=!1},t}(a),M=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.u=g(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){n.prototype.reset.call(this),this.u.reset()},t.prototype.next=function(n,t,r,i){this.u.next(n,t,r,i),this.done=this.u.done,this.keep=!this.u.keep},t}(a),S=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){},t.prototype.next=function(n){o(n)&&n.length===this.params&&(this.done=!0,this.keep=!0)},t}(a),q=function(n){if(0===n.length)throw new Error("$and/$or/$nor must be a nonempty array")},T=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!1,t}return r(t,n),t.prototype.init=function(){var n=this;q(this.params),this.o=this.params.map((function(t){return g(t,null,n.options)}))},t.prototype.reset=function(){this.done=!1,this.keep=!1;for(var n=0,t=this.o.length;n<t;n++)this.o[n].reset()},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.o.length;e<o;e++){var f=this.o[e];if(f.next(n,t,r),f.keep){i=!0,u=f.keep;break}}this.keep=u,this.done=i},t}(a),k=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!1,t}return r(t,n),t.prototype.next=function(t,r,i){n.prototype.next.call(this,t,r,i),this.keep=!this.keep},t}(T),z=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){var n=this,t=Array.isArray(this.params)?this.params:[this.params];this.h=t.map((function(t){if(m(t,n.options))throw new Error("cannot nest $ under ".concat(n.name.toLowerCase()));return b(t,n.options.compare)}))},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.h.length;e<o;e++){if((0,this.h[e])(n)){i=!0,u=!0;break}}this.keep=u,this.done=i},t}(a),F=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,u)||this;return e.propop=!0,e.l=new z(t,r,i,u),e}return r(t,n),t.prototype.next=function(n,t,r,i){this.l.next(n,t,r),o(r)&&!i?this.l.keep?(this.keep=!1,this.done=!0):t==r.length-1&&(this.keep=!0,this.done=!0):(this.keep=!this.l.keep,this.done=!0)},t.prototype.reset=function(){n.prototype.reset.call(this),this.l.reset()},t}(a),N=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.next=function(n,t,r,i,u){u?r.hasOwnProperty(t)===this.params&&(this.done=!0,this.keep=!0):(this.done=!0,this.keep=!this.params)},t}(a),C=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,t.map((function(n){return g(n,r,i)})),u)||this;return e.propop=!1,q(t),e}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(v),D=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,t.map((function(n){return g(n,r,i)})),u)||this;return e.propop=!0,e}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(v),P=function(n,t,r){return new y(n,t,r)},R=function(n,t,r,i){return new _(n,t,r,i)},I=function(n,t,r,i){return new T(n,t,r,i)},U=function(n,t,r,i){return new k(n,t,r,i)},B=function(n,t,r,i){return new A(n,t,r,i)},G=function(n,t,r,i){return new F(n,t,r,i)},H=function(n,t,r,i){return new z(n,t,r,i)},J=d((function(n){return function(t){return null!=t&&t<n}})),K=d((function(n){return function(t){return t===n||t<=n}})),L=d((function(n){return function(t){return null!=t&&t>n}})),Q=d((function(n){return function(t){return t===n||t>=n}})),V=function(n,t,r){var i=n[0],u=n[1];return new y((function(n){return e(n)%i===u}),t,r)},W=function(n,t,r,i){return new N(n,t,r,i)},X=function(n,t,r){return new y(new RegExp(n,t.$options),t,r)},Y=function(n,t,r,i){return new M(n,t,r,i)},Z={number:function(n){return"number"==typeof n},string:function(n){return"string"==typeof n},bool:function(n){return"boolean"==typeof n},array:function(n){return Array.isArray(n)},null:function(n){return null===n},timestamp:function(n){return n instanceof Date}},nn=function(n,t,r){return new y((function(t){if("string"==typeof n){if(!Z[n])throw new Error("Type alias does not exist");return Z[n](t)}return null!=t&&(t instanceof n||t.constructor===n)}),t,r)},tn=function(n,t,r,i){return new C(n,t,r,i)},rn=function(n,t,r,i){return new D(n,t,r,i)},un=function(n,t,r){return new S(n,t,r,"$size")},en=function(){return null},on=function(n,t,r){var i;if(s(n))i=n;else{if(process.env.CSP_ENABLED)throw new Error('In CSP mode, sift does not support strings in "$where" condition');i=new Function("obj","return "+n)}return new y((function(n){return i.bind(n)(n)}),t,r)},fn=Object.freeze({__proto__:null,$Size:S,$all:rn,$and:tn,$elemMatch:B,$eq:P,$exists:W,$gt:L,$gte:Q,$in:H,$lt:J,$lte:K,$mod:V,$ne:R,$nin:G,$nor:U,$not:Y,$options:en,$or:I,$regex:X,$size:un,$type:nn,$where:on}),sn=function(n,t,r){var i=void 0===r?{}:r,u=i.compare,e=i.operations;return g(n,t,{compare:u,operations:Object.assign({},fn,e||{})})};n.$Size=S,n.$all=rn,n.$and=tn,n.$elemMatch=B,n.$eq=P,n.$exists=W,n.$gt=L,n.$gte=Q,n.$in=H,n.$lt=J,n.$lte=K,n.$mod=V,n.$ne=R,n.$nin=G,n.$nor=U,n.$not=Y,n.$options=en,n.$or=I,n.$regex=X,n.$size=un,n.$type=nn,n.$where=on,n.EqualsOperation=y,n.createDefaultQueryOperation=sn,n.createEqualsOperation=function(n,t,r){return new y(n,t,r)},n.createOperationTester=x,n.createQueryOperation=g,n.createQueryTester=function(n,t){return void 0===t&&(t={}),x(g(n,null,t))},n.default=function(n,t){void 0===t&&(t={});var r=sn(n,null,t);return x(r)},Object.defineProperty(n,"v",{value:!0})}));
//# sourceMappingURL=sift.min.js.map
